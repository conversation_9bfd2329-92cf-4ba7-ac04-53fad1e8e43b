# 🐳 Dockerfile for <PERSON><PERSON> Backend

FROM php:8.0-fpm

# 📦 Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip

# 🎼 Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# 👤 Create system user to run Composer and Artisan Commands
RUN useradd -G www-data,root -u 1000 -d /home/<USER>
RUN mkdir -p /home/<USER>/.composer && \
    chown -R accounting:accounting /home/<USER>

# 📁 Set working directory
WORKDIR /var/www

# 📋 Copy existing application directory contents
COPY . /var/www

# 🔧 Copy existing application directory permissions
COPY --chown=accounting:accounting . /var/www

# 👤 Change current user to accounting
USER accounting

# 🚀 Expose port 8000 and start php-fpm server
EXPOSE 8000

CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]

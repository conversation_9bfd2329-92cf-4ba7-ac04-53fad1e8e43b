<?php

namespace App\Services;

use App\Models\Item;
use App\Models\TaxType;

class TaxCalculationService
{
    /**
     * Calculate tax for a single invoice line item.
     */
    public function calculateLineItemTax($item, $quantity, $unitPrice, $discountAmount = 0)
    {
        if (!$item instanceof Item) {
            $item = Item::find($item);
        }

        if (!$item || !$item->is_taxable) {
            return [
                'tax_rate' => 0,
                'tax_amount' => 0,
                'net_amount' => ($unitPrice * $quantity) - $discountAmount,
                'total_amount' => ($unitPrice * $quantity) - $discountAmount,
                'tax_type_id' => null,
            ];
        }

        $netAmount = ($unitPrice * $quantity) - $discountAmount;
        $taxRate = $item->getEffectiveTaxRate();
        $taxAmount = 0;

        if ($item->taxType) {
            $taxAmount = $item->taxType->calculateTax($netAmount, $item->tax_rate);
        } else {
            $taxAmount = $netAmount * ($taxRate / 100);
        }

        return [
            'tax_rate' => $taxRate,
            'tax_amount' => round($taxAmount, 2),
            'net_amount' => round($netAmount, 2),
            'total_amount' => round($netAmount + $taxAmount, 2),
            'tax_type_id' => $item->tax_type_id,
        ];
    }

    /**
     * Calculate total tax for an invoice.
     */
    public function calculateInvoiceTax($invoiceDetails)
    {
        $totalTax = 0;
        $totalNet = 0;
        $totalAmount = 0;
        $taxBreakdown = [];

        foreach ($invoiceDetails as $detail) {
            $item = $detail['item'] ?? Item::find($detail['item_id']);
            $quantity = $detail['quantity'];
            $unitPrice = $detail['unit_price'];
            $discountAmount = $detail['discount_amount'] ?? 0;

            $lineCalculation = $this->calculateLineItemTax($item, $quantity, $unitPrice, $discountAmount);

            $totalNet += $lineCalculation['net_amount'];
            $totalTax += $lineCalculation['tax_amount'];
            $totalAmount += $lineCalculation['total_amount'];

            // تجميع الضرائب حسب النوع
            if ($lineCalculation['tax_type_id']) {
                $taxTypeId = $lineCalculation['tax_type_id'];
                if (!isset($taxBreakdown[$taxTypeId])) {
                    $taxType = TaxType::find($taxTypeId);
                    $taxBreakdown[$taxTypeId] = [
                        'tax_type_name' => $taxType->name,
                        'tax_rate' => $taxType->rate,
                        'taxable_amount' => 0,
                        'tax_amount' => 0,
                    ];
                }
                $taxBreakdown[$taxTypeId]['taxable_amount'] += $lineCalculation['net_amount'];
                $taxBreakdown[$taxTypeId]['tax_amount'] += $lineCalculation['tax_amount'];
            }
        }

        return [
            'subtotal' => round($totalNet, 2),
            'total_tax' => round($totalTax, 2),
            'total_amount' => round($totalAmount, 2),
            'tax_breakdown' => $taxBreakdown,
        ];
    }

    /**
     * Calculate tax with invoice-level discount.
     */
    public function calculateInvoiceTaxWithDiscount($invoiceDetails, $invoiceDiscountAmount = 0, $invoiceDiscountPercentage = 0)
    {
        // حساب الضرائب للبنود أولاً
        $calculation = $this->calculateInvoiceTax($invoiceDetails);
        
        // تطبيق خصم الفاتورة
        $subtotalAfterDiscount = $calculation['subtotal'];
        
        if ($invoiceDiscountPercentage > 0) {
            $invoiceDiscountAmount = $calculation['subtotal'] * ($invoiceDiscountPercentage / 100);
        }
        
        $subtotalAfterDiscount -= $invoiceDiscountAmount;
        
        // إعادة حساب الضرائب بعد الخصم
        $discountRatio = $subtotalAfterDiscount / $calculation['subtotal'];
        $adjustedTaxAmount = $calculation['total_tax'] * $discountRatio;
        
        // تحديث تفصيل الضرائب
        $adjustedTaxBreakdown = [];
        foreach ($calculation['tax_breakdown'] as $taxTypeId => $breakdown) {
            $adjustedTaxBreakdown[$taxTypeId] = [
                'tax_type_name' => $breakdown['tax_type_name'],
                'tax_rate' => $breakdown['tax_rate'],
                'taxable_amount' => round($breakdown['taxable_amount'] * $discountRatio, 2),
                'tax_amount' => round($breakdown['tax_amount'] * $discountRatio, 2),
            ];
        }

        return [
            'subtotal' => round($calculation['subtotal'], 2),
            'discount_amount' => round($invoiceDiscountAmount, 2),
            'subtotal_after_discount' => round($subtotalAfterDiscount, 2),
            'total_tax' => round($adjustedTaxAmount, 2),
            'total_amount' => round($subtotalAfterDiscount + $adjustedTaxAmount, 2),
            'tax_breakdown' => $adjustedTaxBreakdown,
        ];
    }

    /**
     * Validate tax calculation.
     */
    public function validateTaxCalculation($invoiceData)
    {
        $errors = [];

        // التحقق من وجود أصناف خاضعة للضريبة بدون نوع ضريبة
        foreach ($invoiceData['details'] as $detail) {
            $item = Item::find($detail['item_id']);
            if ($item && $item->is_taxable && !$item->tax_type_id && !$item->tax_rate) {
                $errors[] = "الصنف '{$item->name}' خاضع للضريبة ولكن لم يتم تحديد نوع الضريبة أو المعدل";
            }
        }

        // التحقق من صحة معدلات الضريبة
        foreach ($invoiceData['details'] as $detail) {
            if (isset($detail['tax_rate']) && ($detail['tax_rate'] < 0 || $detail['tax_rate'] > 100)) {
                $errors[] = "معدل الضريبة يجب أن يكون بين 0 و 100";
            }
        }

        return [
            'is_valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Generate tax report for a period.
     */
    public function generateTaxReport($fromDate, $toDate, $taxTypeId = null)
    {
        // هذه الدالة ستحتاج إلى تطوير أكثر بعد إنشاء جداول الفواتير
        // يمكن استخدامها لإنشاء تقارير ضريبية دورية
        
        return [
            'period' => [
                'from' => $fromDate,
                'to' => $toDate,
            ],
            'summary' => [
                'total_sales' => 0,
                'taxable_sales' => 0,
                'tax_collected' => 0,
                'tax_breakdown' => [],
            ],
            'details' => [],
        ];
    }

    /**
     * Calculate reverse tax (from inclusive amount).
     */
    public function calculateReverseTax($inclusiveAmount, $taxRate)
    {
        $baseAmount = $inclusiveAmount / (1 + ($taxRate / 100));
        $taxAmount = $inclusiveAmount - $baseAmount;

        return [
            'base_amount' => round($baseAmount, 2),
            'tax_amount' => round($taxAmount, 2),
            'inclusive_amount' => round($inclusiveAmount, 2),
        ];
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\CostCenter;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class CostCenterController extends Controller
{
    /**
     * عرض قائمة مراكز التكلفة
     */
    public function index(Request $request)
    {
        $query = CostCenter::with(['parent', 'children']);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // إذا كان المطلوب المراكز الرئيسية فقط
        if ($request->boolean('parents_only')) {
            $query->whereNull('parent_id');
        }

        $costCenters = $query->orderBy('code')->get();

        // إذا كان المطلوب عرض الشجرة
        if ($request->boolean('tree_format')) {
            $costCenters = $this->buildCostCenterTree($costCenters);
        }

        return response()->json([
            'success' => true,
            'data' => $costCenters,
        ]);
    }

    /**
     * عرض تفاصيل مركز تكلفة محدد
     */
    public function show($id)
    {
        $costCenter = CostCenter::with(['parent', 'children', 'journalEntryDetails'])
                               ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'cost_center' => $costCenter,
                'can_be_deleted' => $costCenter->canBeDeleted(),
            ],
        ]);
    }

    /**
     * إنشاء مركز تكلفة جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:20|unique:cost_centers',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => 'nullable|exists:cost_centers,id',
        ]);

        $costCenter = CostCenter::create([
            'code' => $request->code,
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء مركز التكلفة بنجاح',
            'data' => $costCenter,
        ], 201);
    }

    /**
     * تحديث مركز تكلفة
     */
    public function update(Request $request, $id)
    {
        $costCenter = CostCenter::findOrFail($id);

        $request->validate([
            'code' => ['required', 'string', 'max:20', Rule::unique('cost_centers')->ignore($id)],
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'parent_id' => [
                'nullable',
                'exists:cost_centers,id',
                function ($attribute, $value, $fail) use ($costCenter) {
                    // التأكد من عدم جعل المركز والد لنفسه أو لأحد أحفاده
                    if ($value == $costCenter->id) {
                        $fail('لا يمكن جعل مركز التكلفة والد لنفسه');
                    }
                    
                    if ($value && $costCenter->descendants()->pluck('id')->contains($value)) {
                        $fail('لا يمكن جعل مركز التكلفة والد لأحد أحفاده');
                    }
                },
            ],
        ]);

        $costCenter->update([
            'code' => $request->code,
            'name' => $request->name,
            'description' => $request->description,
            'parent_id' => $request->parent_id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث مركز التكلفة بنجاح',
            'data' => $costCenter,
        ]);
    }

    /**
     * حذف مركز تكلفة
     */
    public function destroy($id)
    {
        $costCenter = CostCenter::findOrFail($id);

        if (!$costCenter->canBeDeleted()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف هذا المركز لأنه يحتوي على مراكز فرعية أو قيود محاسبية',
            ], 422);
        }

        $costCenter->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف مركز التكلفة بنجاح',
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل مركز تكلفة
     */
    public function toggleStatus($id)
    {
        $costCenter = CostCenter::findOrFail($id);
        $costCenter->update(['is_active' => !$costCenter->is_active]);

        $status = $costCenter->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return response()->json([
            'success' => true,
            'message' => "{$status} مركز التكلفة بنجاح",
            'data' => $costCenter,
        ]);
    }

    /**
     * الحصول على تقرير مركز التكلفة
     */
    public function report(Request $request, $id)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
        ]);

        $costCenter = CostCenter::findOrFail($id);
        $fromDate = $request->from_date ?? now()->startOfMonth()->toDateString();
        $toDate = $request->to_date ?? now()->toDateString();

        // الحركات خلال الفترة
        $movements = $costCenter->journalEntryDetails()
                               ->with(['journalEntry', 'account'])
                               ->whereHas('journalEntry', function ($query) use ($fromDate, $toDate) {
                                   $query->whereBetween('entry_date', [$fromDate, $toDate])
                                         ->where('status', 'approved');
                               })
                               ->orderBy('created_at')
                               ->get();

        // تجميع حسب نوع الحساب
        $summary = [
            'total_debit' => $movements->sum('debit'),
            'total_credit' => $movements->sum('credit'),
            'net_amount' => $movements->sum('debit') - $movements->sum('credit'),
            'by_account_type' => $movements->groupBy('account.account_type')->map(function ($group) {
                return [
                    'total_debit' => $group->sum('debit'),
                    'total_credit' => $group->sum('credit'),
                    'net_amount' => $group->sum('debit') - $group->sum('credit'),
                ];
            }),
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'cost_center' => $costCenter,
                'period' => [
                    'from' => $fromDate,
                    'to' => $toDate,
                ],
                'summary' => $summary,
                'movements' => $movements,
            ],
        ]);
    }

    /**
     * الحصول على مراكز التكلفة النشطة فقط
     */
    public function getActiveCostCenters()
    {
        $costCenters = CostCenter::active()
                                ->orderBy('code')
                                ->get(['id', 'code', 'name']);

        return response()->json([
            'success' => true,
            'data' => $costCenters,
        ]);
    }

    /**
     * بناء شجرة مراكز التكلفة
     */
    protected function buildCostCenterTree($costCenters, $parentId = null)
    {
        $tree = [];
        
        foreach ($costCenters as $costCenter) {
            if ($costCenter->parent_id == $parentId) {
                $children = $this->buildCostCenterTree($costCenters, $costCenter->id);
                if ($children) {
                    $costCenter->children = $children;
                }
                $tree[] = $costCenter;
            }
        }
        
        return $tree;
    }
}

{"name": "illuminate/database", "description": "The Illuminate Database package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "keywords": ["laravel", "database", "sql", "orm"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-pdo": "*", "brick/math": "^0.9.3|^0.10.2|^0.11", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "symfony/console": "^6.0.9"}, "autoload": {"psr-4": {"Illuminate\\Database\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"ext-filter": "Required to use the Postgres database driver.", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.13.3|^3.1.4).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.21).", "illuminate/console": "Required to use the database commands (^9.0).", "illuminate/events": "Required to use the observers with Eloquent (^9.0).", "illuminate/filesystem": "Required to use the migrations (^9.0).", "illuminate/pagination": "Required to paginate the result set (^9.0).", "symfony/finder": "Required to use Eloquent model factories (^6.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
<?php

namespace App\Http\Controllers;

use App\Models\Supplier;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class SupplierController extends Controller
{
    /**
     * عرض قائمة الموردين
     */
    public function index(Request $request)
    {
        $query = Supplier::query();

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $suppliers = $query->orderBy('name')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $suppliers,
        ]);
    }

    /**
     * عرض تفاصيل مورد محدد
     */
    public function show($id)
    {
        $supplier = Supplier::with(['purchaseInvoices', 'payments'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'supplier' => $supplier,
                'aging_analysis' => $supplier->getAgingAnalysis(),
                'overdue_invoices_count' => $supplier->getOverdueInvoices()->count(),
                'top_purchased_items' => $supplier->getTopPurchasedItems(5),
            ],
        ]);
    }

    /**
     * إنشاء مورد جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'tax_number' => 'nullable|string|max:50',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'opening_balance' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        $supplier = Supplier::create([
            'code' => Supplier::generateSupplierCode(),
            'name' => $request->name,
            'name_en' => $request->name_en,
            'phone' => $request->phone,
            'email' => $request->email,
            'address' => $request->address,
            'tax_number' => $request->tax_number,
            'payment_terms' => $request->payment_terms ?? 30,
            'opening_balance' => $request->opening_balance ?? 0,
            'current_balance' => $request->opening_balance ?? 0,
            'notes' => $request->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء المورد بنجاح',
            'data' => $supplier,
        ], 201);
    }

    /**
     * تحديث مورد
     */
    public function update(Request $request, $id)
    {
        $supplier = Supplier::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'tax_number' => 'nullable|string|max:50',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'notes' => 'nullable|string',
        ]);

        $supplier->update($request->only([
            'name', 'name_en', 'phone', 'email', 'address', 'tax_number',
            'payment_terms', 'notes'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث المورد بنجاح',
            'data' => $supplier,
        ]);
    }

    /**
     * حذف مورد
     */
    public function destroy($id)
    {
        $supplier = Supplier::findOrFail($id);

        // التحقق من وجود فواتير
        if ($supplier->purchaseInvoices()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف المورد لأنه يحتوي على فواتير',
            ], 422);
        }

        $supplier->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف المورد بنجاح',
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل مورد
     */
    public function toggleStatus($id)
    {
        $supplier = Supplier::findOrFail($id);
        $supplier->update(['is_active' => !$supplier->is_active]);

        $status = $supplier->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return response()->json([
            'success' => true,
            'message' => "{$status} المورد بنجاح",
            'data' => $supplier,
        ]);
    }

    /**
     * كشف حساب المورد
     */
    public function statement(Request $request, $id)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
        ]);

        $supplier = Supplier::findOrFail($id);
        $statement = $supplier->getStatement($request->from_date, $request->to_date);

        return response()->json([
            'success' => true,
            'data' => $statement,
        ]);
    }

    /**
     * تحليل الأعمار للمورد
     */
    public function agingAnalysis($id)
    {
        $supplier = Supplier::findOrFail($id);
        $aging = $supplier->getAgingAnalysis();

        return response()->json([
            'success' => true,
            'data' => [
                'supplier' => $supplier,
                'aging' => $aging,
                'total_outstanding' => array_sum($aging),
            ],
        ]);
    }

    /**
     * الحصول على الموردين النشطين فقط
     */
    public function getActiveSuppliers()
    {
        $suppliers = Supplier::active()
                           ->orderBy('name')
                           ->get(['id', 'code', 'name', 'phone', 'current_balance']);

        return response()->json([
            'success' => true,
            'data' => $suppliers,
        ]);
    }

    /**
     * إحصائيات الموردين
     */
    public function getStatistics()
    {
        $stats = [
            'total_suppliers' => Supplier::count(),
            'active_suppliers' => Supplier::active()->count(),
            'total_payables' => Supplier::sum('current_balance'),
            'average_balance' => Supplier::avg('current_balance'),
            'suppliers_with_overdue' => Supplier::whereHas('purchaseInvoices', function ($query) {
                $query->where('status', 'approved')
                      ->where('payment_status', '!=', 'paid')
                      ->whereRaw('DATE_ADD(invoice_date, INTERVAL payment_terms DAY) < ?', [now()->toDateString()]);
            })->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * تقرير أفضل الموردين
     */
    public function topSuppliersReport(Request $request)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        $fromDate = $request->from_date ?? now()->startOfYear()->toDateString();
        $toDate = $request->to_date ?? now()->toDateString();
        $limit = $request->limit ?? 10;

        $suppliers = Supplier::withSum(['purchaseInvoices as total_purchases' => function ($query) use ($fromDate, $toDate) {
                                $query->whereBetween('invoice_date', [$fromDate, $toDate])
                                      ->where('status', 'approved');
                            }], 'total_amount')
                            ->having('total_purchases', '>', 0)
                            ->orderByDesc('total_purchases')
                            ->limit($limit)
                            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'period' => ['from' => $fromDate, 'to' => $toDate],
                'suppliers' => $suppliers,
                'total_purchases' => $suppliers->sum('total_purchases'),
            ],
        ]);
    }
}

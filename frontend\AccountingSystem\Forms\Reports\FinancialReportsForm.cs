using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Reports
{
    public partial class FinancialReportsForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;
        private TabControl? _reportsTabControl;
        private DateTimePicker? _fromDatePicker;
        private DateTimePicker? _toDatePicker;
        private DateTimePicker? _asOfDatePicker;
        private ComboBox? _branchComboBox;
        private Button? _generateButton;
        private Button? _exportButton;
        private Button? _printButton;
        private DataGridView? _reportGrid;
        private RichTextBox? _reportText;

        public FinancialReportsForm(IApiService apiService, IAuthService authService)
        {
            _apiService = apiService;
            _authService = authService;
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "التقارير المالية";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Parameters panel
            var parametersPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Date range
            var fromDateLabel = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(1050, 15),
                Size = new Size(60, 20)
            };

            _fromDatePicker = new DateTimePicker
            {
                Location = new Point(900, 12),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(-1)
            };

            var toDateLabel = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(820, 15),
                Size = new Size(60, 20)
            };

            _toDatePicker = new DateTimePicker
            {
                Location = new Point(670, 12),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // As of date for balance sheet
            var asOfDateLabel = new Label
            {
                Text = "كما في تاريخ:",
                Location = new Point(570, 15),
                Size = new Size(80, 20)
            };

            _asOfDatePicker = new DateTimePicker
            {
                Location = new Point(420, 12),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Branch filter
            var branchLabel = new Label
            {
                Text = "الفرع:",
                Location = new Point(350, 15),
                Size = new Size(50, 20)
            };

            _branchComboBox = new ComboBox
            {
                Location = new Point(200, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _branchComboBox.Items.Add(new { Text = "جميع الفروع", Value = "" });
            _branchComboBox.DisplayMember = "Text";
            _branchComboBox.ValueMember = "Value";
            _branchComboBox.SelectedIndex = 0;

            // Buttons
            _generateButton = new Button
            {
                Text = "إنشاء التقرير",
                Location = new Point(10, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _exportButton = new Button
            {
                Text = "تصدير",
                Location = new Point(10, 50),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _printButton = new Button
            {
                Text = "طباعة",
                Location = new Point(100, 50),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(13, 110, 253),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            parametersPanel.Controls.AddRange(new Control[]
            {
                fromDateLabel, _fromDatePicker, toDateLabel, _toDatePicker,
                asOfDateLabel, _asOfDatePicker, branchLabel, _branchComboBox,
                _generateButton, _exportButton, _printButton
            });

            // Reports tab control
            _reportsTabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10)
            };

            CreateReportTabs();

            this.Controls.AddRange(new Control[] { _reportsTabControl, parametersPanel });
        }

        private void CreateReportTabs()
        {
            if (_reportsTabControl == null) return;

            // Income Statement Tab
            var incomeStatementTab = new TabPage("قائمة الدخل");
            var incomeStatementGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            SetupIncomeStatementGrid(incomeStatementGrid);
            incomeStatementTab.Controls.Add(incomeStatementGrid);

            // Balance Sheet Tab
            var balanceSheetTab = new TabPage("الميزانية العمومية");
            var balanceSheetGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            SetupBalanceSheetGrid(balanceSheetGrid);
            balanceSheetTab.Controls.Add(balanceSheetGrid);

            // Cash Flow Tab
            var cashFlowTab = new TabPage("قائمة التدفقات النقدية");
            var cashFlowGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            SetupCashFlowGrid(cashFlowGrid);
            cashFlowTab.Controls.Add(cashFlowGrid);

            // Trial Balance Tab
            var trialBalanceTab = new TabPage("ميزان المراجعة");
            var trialBalanceGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };
            SetupTrialBalanceGrid(trialBalanceGrid);
            trialBalanceTab.Controls.Add(trialBalanceGrid);

            // Financial Ratios Tab
            var ratiosTab = new TabPage("النسب المالية");
            _reportText = new RichTextBox
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11),
                ReadOnly = true,
                BackColor = Color.White
            };
            ratiosTab.Controls.Add(_reportText);

            _reportsTabControl.TabPages.AddRange(new TabPage[]
            {
                incomeStatementTab, balanceSheetTab, cashFlowTab, trialBalanceTab, ratiosTab
            });
        }

        private void SetupIncomeStatementGrid(DataGridView grid)
        {
            grid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "البند",
                    Width = 300
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CurrentPeriod",
                    HeaderText = "الفترة الحالية",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "PreviousPeriod",
                    HeaderText = "الفترة السابقة",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Variance",
                    HeaderText = "التغيير",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "VariancePercent",
                    HeaderText = "نسبة التغيير %",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N1", Alignment = DataGridViewContentAlignment.MiddleRight }
                }
            });
        }

        private void SetupBalanceSheetGrid(DataGridView grid)
        {
            grid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "البند",
                    Width = 300
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CurrentYear",
                    HeaderText = "السنة الحالية",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "PreviousYear",
                    HeaderText = "السنة السابقة",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Variance",
                    HeaderText = "التغيير",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                }
            });
        }

        private void SetupCashFlowGrid(DataGridView grid)
        {
            grid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "ActivityType",
                    HeaderText = "نوع النشاط",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "الوصف",
                    Width = 300
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Amount",
                    HeaderText = "المبلغ",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                }
            });
        }

        private void SetupTrialBalanceGrid(DataGridView grid)
        {
            grid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "كود الحساب",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 250
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "DebitBalance",
                    HeaderText = "رصيد مدين",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditBalance",
                    HeaderText = "رصيد دائن",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2", Alignment = DataGridViewContentAlignment.MiddleRight }
                }
            });
        }

        private void SetupLayout()
        {
            // Additional layout setup if needed
        }

        private void SetupEvents()
        {
            if (_generateButton != null)
                _generateButton.Click += GenerateButton_Click;

            if (_exportButton != null)
                _exportButton.Click += ExportButton_Click;

            if (_printButton != null)
                _printButton.Click += PrintButton_Click;

            if (_reportsTabControl != null)
                _reportsTabControl.SelectedIndexChanged += ReportsTabControl_SelectedIndexChanged;
        }

        private async void GenerateButton_Click(object? sender, EventArgs e)
        {
            if (_reportsTabControl == null) return;

            try
            {
                this.Cursor = Cursors.WaitCursor;
                _generateButton!.Enabled = false;
                _generateButton.Text = "جاري الإنشاء...";

                var selectedTab = _reportsTabControl.SelectedTab;
                if (selectedTab == null) return;

                switch (selectedTab.Text)
                {
                    case "قائمة الدخل":
                        await GenerateIncomeStatement();
                        break;
                    case "الميزانية العمومية":
                        await GenerateBalanceSheet();
                        break;
                    case "قائمة التدفقات النقدية":
                        await GenerateCashFlowStatement();
                        break;
                    case "ميزان المراجعة":
                        await GenerateTrialBalance();
                        break;
                    case "النسب المالية":
                        await GenerateFinancialRatios();
                        break;
                }

                _exportButton!.Enabled = true;
                _printButton!.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                _generateButton!.Enabled = true;
                _generateButton.Text = "إنشاء التقرير";
            }
        }

        private async Task GenerateIncomeStatement()
        {
            var fromDate = _fromDatePicker!.Value.ToString("yyyy-MM-dd");
            var toDate = _toDatePicker!.Value.ToString("yyyy-MM-dd");
            var branchId = (_branchComboBox!.SelectedItem as dynamic)?.Value?.ToString();

            var endpoint = $"financial-reports/income-statement?from_date={fromDate}&to_date={toDate}";
            if (!string.IsNullOrEmpty(branchId))
                endpoint += $"&branch_id={branchId}";

            var response = await _apiService.GetAsync<object>(endpoint);

            if (response.Success)
            {
                MessageBox.Show("تم إنشاء قائمة الدخل بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // TODO: Populate grid with actual data
            }
            else
            {
                MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task GenerateBalanceSheet()
        {
            var asOfDate = _asOfDatePicker!.Value.ToString("yyyy-MM-dd");
            var branchId = (_branchComboBox!.SelectedItem as dynamic)?.Value?.ToString();

            var endpoint = $"financial-reports/balance-sheet?as_of_date={asOfDate}";
            if (!string.IsNullOrEmpty(branchId))
                endpoint += $"&branch_id={branchId}";

            var response = await _apiService.GetAsync<object>(endpoint);

            if (response.Success)
            {
                MessageBox.Show("تم إنشاء الميزانية العمومية بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // TODO: Populate grid with actual data
            }
            else
            {
                MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task GenerateCashFlowStatement()
        {
            var fromDate = _fromDatePicker!.Value.ToString("yyyy-MM-dd");
            var toDate = _toDatePicker!.Value.ToString("yyyy-MM-dd");
            var branchId = (_branchComboBox!.SelectedItem as dynamic)?.Value?.ToString();

            var endpoint = $"financial-reports/cash-flow?from_date={fromDate}&to_date={toDate}";
            if (!string.IsNullOrEmpty(branchId))
                endpoint += $"&branch_id={branchId}";

            var response = await _apiService.GetAsync<object>(endpoint);

            if (response.Success)
            {
                MessageBox.Show("تم إنشاء قائمة التدفقات النقدية بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                // TODO: Populate grid with actual data
            }
            else
            {
                MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task GenerateTrialBalance()
        {
            MessageBox.Show("سيتم إنشاء ميزان المراجعة", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async Task GenerateFinancialRatios()
        {
            var asOfDate = _asOfDatePicker!.Value.ToString("yyyy-MM-dd");
            var branchId = (_branchComboBox!.SelectedItem as dynamic)?.Value?.ToString();

            var endpoint = $"financial-reports/financial-ratios?as_of_date={asOfDate}";
            if (!string.IsNullOrEmpty(branchId))
                endpoint += $"&branch_id={branchId}";

            var response = await _apiService.GetAsync<object>(endpoint);

            if (response.Success)
            {
                if (_reportText != null)
                {
                    _reportText.Text = "تم إنشاء تقرير النسب المالية بنجاح\n\n";
                    _reportText.Text += "سيتم عرض النسب المالية هنا...";
                }
            }
            else
            {
                MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void PrintButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم طباعة التقرير", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ReportsTabControl_SelectedIndexChanged(object? sender, EventArgs e)
        {
            // Reset export and print buttons when tab changes
            if (_exportButton != null) _exportButton.Enabled = false;
            if (_printButton != null) _printButton.Enabled = false;
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supplier extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'phone',
        'email',
        'address',
        'tax_number',
        'payment_terms',
        'opening_balance',
        'current_balance',
        'is_active',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payment_terms' => 'integer',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the purchase invoices for this supplier.
     */
    public function purchaseInvoices()
    {
        return $this->hasMany(PurchaseInvoice::class);
    }

    /**
     * Get the payments made to this supplier.
     */
    public function payments()
    {
        return $this->hasMany(SupplierPayment::class);
    }

    /**
     * Scope a query to only include active suppliers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Update supplier balance.
     */
    public function updateBalance($amount, $type = 'credit')
    {
        if ($type === 'credit') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    /**
     * Get supplier statement.
     */
    public function getStatement($fromDate = null, $toDate = null)
    {
        $fromDate = $fromDate ?? now()->startOfMonth()->toDateString();
        $toDate = $toDate ?? now()->toDateString();

        $invoices = $this->purchaseInvoices()
                        ->whereBetween('invoice_date', [$fromDate, $toDate])
                        ->where('status', 'approved')
                        ->orderBy('invoice_date')
                        ->get();

        $payments = $this->payments()
                        ->whereBetween('payment_date', [$fromDate, $toDate])
                        ->orderBy('payment_date')
                        ->get();

        // دمج الفواتير والمدفوعات وترتيبها حسب التاريخ
        $transactions = collect();

        foreach ($invoices as $invoice) {
            $transactions->push([
                'date' => $invoice->invoice_date,
                'type' => 'invoice',
                'reference' => $invoice->invoice_number,
                'description' => 'فاتورة مشتريات',
                'debit' => 0,
                'credit' => $invoice->total_amount,
                'data' => $invoice,
            ]);
        }

        foreach ($payments as $payment) {
            $transactions->push([
                'date' => $payment->payment_date,
                'type' => 'payment',
                'reference' => $payment->payment_number,
                'description' => 'دفعة للمورد',
                'debit' => $payment->amount,
                'credit' => 0,
                'data' => $payment,
            ]);
        }

        $transactions = $transactions->sortBy('date')->values();

        // حساب الرصيد الجاري
        $runningBalance = $this->opening_balance;
        foreach ($transactions as $key => $transaction) {
            $runningBalance += $transaction['credit'] - $transaction['debit'];
            $transactions[$key]['running_balance'] = $runningBalance;
        }

        return [
            'supplier' => $this,
            'period' => ['from' => $fromDate, 'to' => $toDate],
            'opening_balance' => $this->opening_balance,
            'closing_balance' => $runningBalance,
            'transactions' => $transactions,
            'summary' => [
                'total_invoices' => $invoices->sum('total_amount'),
                'total_payments' => $payments->sum('amount'),
                'net_change' => $invoices->sum('total_amount') - $payments->sum('amount'),
            ],
        ];
    }

    /**
     * Generate unique supplier code.
     */
    public static function generateSupplierCode()
    {
        $lastSupplier = static::orderBy('code', 'desc')->first();
        
        if ($lastSupplier) {
            $lastNumber = (int) substr($lastSupplier->code, 4); // Remove "SUPP" prefix
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return 'SUPP' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get overdue invoices.
     */
    public function getOverdueInvoices()
    {
        return $this->purchaseInvoices()
                   ->where('status', 'approved')
                   ->where('payment_status', '!=', 'paid')
                   ->whereRaw('DATE_ADD(invoice_date, INTERVAL payment_terms DAY) < ?', [now()->toDateString()])
                   ->get();
    }

    /**
     * Calculate aging analysis.
     */
    public function getAgingAnalysis()
    {
        $overdueInvoices = $this->getOverdueInvoices();
        $today = now();

        $aging = [
            'current' => 0,      // 0-30 days
            '30_days' => 0,      // 31-60 days
            '60_days' => 0,      // 61-90 days
            '90_days_plus' => 0, // 90+ days
        ];

        foreach ($overdueInvoices as $invoice) {
            $dueDate = $invoice->invoice_date->addDays($this->payment_terms);
            $daysOverdue = $today->diffInDays($dueDate);
            $remainingAmount = $invoice->total_amount - $invoice->paid_amount;

            if ($daysOverdue <= 30) {
                $aging['current'] += $remainingAmount;
            } elseif ($daysOverdue <= 60) {
                $aging['30_days'] += $remainingAmount;
            } elseif ($daysOverdue <= 90) {
                $aging['60_days'] += $remainingAmount;
            } else {
                $aging['90_days_plus'] += $remainingAmount;
            }
        }

        return $aging;
    }

    /**
     * Get top purchased items from this supplier.
     */
    public function getTopPurchasedItems($limit = 10)
    {
        return $this->purchaseInvoices()
                   ->with('details.item')
                   ->where('status', 'approved')
                   ->get()
                   ->flatMap(function ($invoice) {
                       return $invoice->details;
                   })
                   ->groupBy('item_id')
                   ->map(function ($details) {
                       return [
                           'item' => $details->first()->item,
                           'total_quantity' => $details->sum('quantity'),
                           'total_amount' => $details->sum('total_amount'),
                           'average_price' => $details->avg('unit_price'),
                       ];
                   })
                   ->sortByDesc('total_amount')
                   ->take($limit)
                   ->values();
    }
}

# 🖥️ نظام المحاسبة المتكامل - تطبيق سطح المكتب

## 📋 نظرة عامة

تطبيق سطح مكتب متقدم مبني بـ C# .NET 6 WinForms يوفر واجهة مستخدم حديثة وسهلة الاستخدام لنظام المحاسبة المتكامل.

## 🏗️ التقنيات المستخدمة

- **.NET 6** - إطار العمل الأساسي
- **WinForms** - واجهة المستخدم
- **RestSharp** - للتواصل مع API
- **Newtonsoft.Json** - لمعالجة JSON
- **Microsoft.Extensions.DependencyInjection** - حقن التبعيات
- **Microsoft.Extensions.Configuration** - إدارة الإعدادات
- **Microsoft.Extensions.Logging** - نظام السجلات

## 🚀 المتطلبات

- Windows 10/11
- .NET 6.0 Runtime أو أحدث
- Visual Studio 2022 (للتطوير)
- اتصال بالإنترنت للوصول لـ API

## 📦 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd frontend/AccountingSystem
```

### 2. تثبيت التبعيات
```bash
dotnet restore
```

### 3. إعداد الإعدادات
قم بتحديث ملف `appsettings.json`:
```json
{
  "ApiSettings": {
    "BaseUrl": "http://localhost:8000/api",
    "Timeout": 30,
    "RetryCount": 3
  }
}
```

### 4. بناء المشروع
```bash
dotnet build
```

### 5. تشغيل التطبيق
```bash
dotnet run
```

## 🔧 الميزات الرئيسية

### ✅ **نظام المصادقة**
- تسجيل دخول آمن
- حفظ بيانات المستخدم
- إدارة الجلسات
- تسجيل خروج تلقائي

### ✅ **واجهة المستخدم الحديثة**
- تصميم عصري ومتجاوب
- دعم اللغة العربية (RTL)
- قوائم منظمة ومنطقية
- شريط حالة معلوماتي

### ✅ **إدارة البيانات**
- عرض البيانات في جداول متقدمة
- بحث وفلترة سريعة
- تحديث تلقائي للبيانات
- معالجة الأخطاء بذكاء

### ✅ **التكامل مع API**
- اتصال آمن مع الخادم
- معالجة الاستجابات
- إدارة الأخطاء
- إعادة المحاولة التلقائية

## 📱 الشاشات المتاحة

### 🔐 **شاشة تسجيل الدخول**
- واجهة بسيطة وأنيقة
- التحقق من صحة البيانات
- حفظ بيانات المستخدم
- رسائل خطأ واضحة

### 🏠 **الشاشة الرئيسية**
- قوائم شاملة لجميع الوحدات
- شريط حالة معلوماتي
- إدارة النوافذ المتعددة (MDI)
- تسجيل خروج آمن

### 📚 **دليل الحسابات**
- عرض شجري للحسابات
- جدول تفصيلي للبيانات
- بحث وفلترة متقدمة
- إضافة وتعديل الحسابات

## 🔧 البنية التقنية

### **Services (الخدمات)**
```csharp
IApiService          // خدمة التواصل مع API
IAuthService         // خدمة المصادقة والتفويض
```

### **Models (النماذج)**
```csharp
BaseModel           // النموذج الأساسي
User                // نموذج المستخدم
Account             // نموذج الحساب
Customer            // نموذج العميل
Supplier            // نموذج المورد
```

### **Forms (النماذج)**
```csharp
LoginForm           // نموذج تسجيل الدخول
MainForm            // النموذج الرئيسي
AccountsForm        // نموذج دليل الحسابات
```

## ⚙️ الإعدادات

### **appsettings.json**
```json
{
  "ApiSettings": {
    "BaseUrl": "http://localhost:8000/api",
    "Timeout": 30,
    "RetryCount": 3
  },
  "AppSettings": {
    "Language": "ar",
    "Theme": "Light",
    "CompanyName": "شركة التقنيات المتقدمة",
    "AutoSave": true,
    "AutoSaveInterval": 300
  },
  "SecuritySettings": {
    "SessionTimeout": 3600,
    "MaxLoginAttempts": 3,
    "PasswordMinLength": 8
  }
}
```

## 🔒 الأمان

### **المصادقة**
- تشفير كلمات المرور
- رموز الوصول المؤقتة
- انتهاء صلاحية الجلسات
- حماية من الهجمات

### **التفويض**
- فحص الصلاحيات
- إخفاء الوظائف غير المسموحة
- رسائل خطأ واضحة
- سجل العمليات

## 🧪 الاختبار

### **تشغيل الاختبارات**
```bash
dotnet test
```

### **أنواع الاختبارات**
- اختبارات الوحدة (Unit Tests)
- اختبارات التكامل (Integration Tests)
- اختبارات واجهة المستخدم (UI Tests)

## 📊 الأداء

### **التحسينات**
- تحميل البيانات بشكل تدريجي
- ذاكرة تخزين مؤقت ذكية
- ضغط البيانات المنقولة
- إدارة الذاكرة بكفاءة

### **المراقبة**
- سجلات مفصلة للعمليات
- مراقبة استخدام الذاكرة
- قياس أوقات الاستجابة
- تتبع الأخطاء

## 🛠️ التطوير

### **إضافة نموذج جديد**
1. إنشاء Model في مجلد Models
2. إنشاء Form في المجلد المناسب
3. إضافة Service إذا لزم الأمر
4. تحديث MainForm لإضافة القائمة

### **إضافة خدمة جديدة**
1. إنشاء Interface في مجلد Services
2. تنفيذ الخدمة
3. تسجيل الخدمة في Program.cs
4. حقن الخدمة في النماذج المطلوبة

## 🐛 استكشاف الأخطاء

### **مشاكل شائعة**
- **خطأ الاتصال بـ API**: تحقق من BaseUrl في الإعدادات
- **مشكلة المصادقة**: تحقق من صحة بيانات تسجيل الدخول
- **بطء التطبيق**: تحقق من سرعة الإنترنت والخادم

### **السجلات**
- سجلات التطبيق في مجلد Logs
- سجلات API في وحدة التحكم
- سجلات الأخطاء في Event Viewer

## 📞 الدعم الفني

للحصول على المساعدة:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966112345678
- الموقع: www.company.com/support

---

**تم تطوير هذا التطبيق باستخدام أحدث التقنيات وأفضل الممارسات في البرمجة.**

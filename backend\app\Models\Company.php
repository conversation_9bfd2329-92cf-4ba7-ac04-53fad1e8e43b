<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_en',
        'logo',
        'tax_number',
        'commercial_register',
        'address',
        'phone',
        'email',
        'website',
        'currency',
        'fiscal_year_start',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'fiscal_year_start' => 'date',
    ];

    /**
     * Get the branches for the company.
     */
    public function branches()
    {
        return $this->hasMany(Branch::class);
    }

    /**
     * Get the active branches for the company.
     */
    public function activeBranches()
    {
        return $this->hasMany(Branch::class)->where('is_active', true);
    }
}

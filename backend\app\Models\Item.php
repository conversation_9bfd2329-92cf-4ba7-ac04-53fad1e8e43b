<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'barcode',
        'name',
        'name_en',
        'description',
        'category_id',
        'unit_id',
        'cost_price',
        'selling_price',
        'min_stock',
        'max_stock',
        'reorder_level',
        'is_active',
        'has_serial',
        'has_expiry',
        'is_taxable',
        'tax_type_id',
        'tax_rate',
        'image',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'min_stock' => 'decimal:3',
        'max_stock' => 'decimal:3',
        'reorder_level' => 'decimal:3',
        'is_active' => 'boolean',
        'has_serial' => 'boolean',
        'has_expiry' => 'boolean',
        'is_taxable' => 'boolean',
        'tax_rate' => 'decimal:2',
    ];

    /**
     * Get the category that owns the item.
     */
    public function category()
    {
        return $this->belongsTo(ItemCategory::class, 'category_id');
    }

    /**
     * Get the unit that owns the item.
     */
    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the tax type for this item.
     */
    public function taxType()
    {
        return $this->belongsTo(TaxType::class);
    }

    /**
     * Get the sales invoice details for this item.
     */
    public function salesInvoiceDetails()
    {
        return $this->hasMany(SalesInvoiceDetail::class);
    }

    /**
     * Get the purchase invoice details for this item.
     */
    public function purchaseInvoiceDetails()
    {
        return $this->hasMany(PurchaseInvoiceDetail::class);
    }

    /**
     * Get the stock movements for this item.
     */
    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Scope a query to only include active items.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include taxable items.
     */
    public function scopeTaxable($query)
    {
        return $query->where('is_taxable', true);
    }

    /**
     * Scope a query to only include items with low stock.
     */
    public function scopeLowStock($query)
    {
        return $query->whereRaw('current_stock <= reorder_level');
    }

    /**
     * Calculate tax amount for this item.
     */
    public function calculateTax($baseAmount, $quantity = 1)
    {
        if (!$this->is_taxable) {
            return 0;
        }

        $totalBaseAmount = $baseAmount * $quantity;

        if ($this->tax_rate) {
            // استخدام معدل الضريبة المخصص للصنف
            return $totalBaseAmount * ($this->tax_rate / 100);
        } elseif ($this->taxType) {
            // استخدام نوع الضريبة المحدد
            return $this->taxType->calculateTax($totalBaseAmount);
        }

        return 0;
    }

    /**
     * Calculate total price including tax.
     */
    public function calculateTotalPrice($basePrice = null, $quantity = 1)
    {
        $price = $basePrice ?? $this->selling_price;
        $baseAmount = $price * $quantity;
        
        if (!$this->is_taxable) {
            return $baseAmount;
        }

        $taxAmount = $this->calculateTax($price, $quantity);
        return $baseAmount + $taxAmount;
    }

    /**
     * Get effective tax rate for this item.
     */
    public function getEffectiveTaxRate()
    {
        if (!$this->is_taxable) {
            return 0;
        }

        if ($this->tax_rate) {
            return $this->tax_rate;
        } elseif ($this->taxType) {
            return $this->taxType->rate;
        }

        return 0;
    }

    /**
     * Get tax information for this item.
     */
    public function getTaxInfo()
    {
        if (!$this->is_taxable) {
            return [
                'is_taxable' => false,
                'tax_type' => null,
                'tax_rate' => 0,
                'tax_name' => 'معفى من الضريبة',
            ];
        }

        return [
            'is_taxable' => true,
            'tax_type' => $this->taxType,
            'tax_rate' => $this->getEffectiveTaxRate(),
            'tax_name' => $this->taxType ? $this->taxType->name : 'ضريبة مخصصة',
        ];
    }

    /**
     * Check if item needs reordering.
     */
    public function needsReorder()
    {
        return $this->current_stock <= $this->reorder_level;
    }

    /**
     * Get current stock level.
     */
    public function getCurrentStock($warehouseId = null)
    {
        $query = $this->stockMovements();
        
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        $movements = $query->get();
        $stock = 0;

        foreach ($movements as $movement) {
            if ($movement->type === 'in') {
                $stock += $movement->quantity;
            } else {
                $stock -= $movement->quantity;
            }
        }

        return $stock;
    }

    /**
     * Generate unique item code.
     */
    public static function generateItemCode($categoryId = null)
    {
        $prefix = 'ITM';
        
        if ($categoryId) {
            $category = ItemCategory::find($categoryId);
            if ($category) {
                $prefix = strtoupper(substr($category->name, 0, 3));
            }
        }

        $lastItem = static::where('code', 'like', "{$prefix}-%")
                          ->orderBy('code', 'desc')
                          ->first();

        if ($lastItem) {
            $lastNumber = (int) substr($lastItem->code, -6);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . '-' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }
}

using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Accounting
{
    public partial class JournalEntriesForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;
        private DataGridView? _entriesGrid;
        private DataGridView? _detailsGrid;
        private SplitContainer? _splitContainer;
        private TextBox? _searchTextBox;
        private DateTimePicker? _fromDatePicker;
        private DateTimePicker? _toDatePicker;
        private ComboBox? _statusComboBox;
        private Button? _addButton;
        private Button? _editButton;
        private Button? _deleteButton;
        private Button? _approveButton;
        private Button? _reverseButton;
        private Button? _refreshButton;
        private List<JournalEntry> _journalEntries = new();

        public JournalEntriesForm(IApiService apiService, IAuthService authService)
        {
            _apiService = apiService;
            _authService = authService;
            InitializeComponent();
            SetupForm();
            LoadJournalEntriesAsync();
        }

        private void SetupForm()
        {
            this.Text = "القيود المحاسبية";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Filter panel
            var filterPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Search
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1050, 15),
                Size = new Size(50, 20)
            };

            _searchTextBox = new TextBox
            {
                Location = new Point(850, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Date filters
            var fromDateLabel = new Label
            {
                Text = "من تاريخ:",
                Location = new Point(750, 15),
                Size = new Size(60, 20)
            };

            _fromDatePicker = new DateTimePicker
            {
                Location = new Point(600, 12),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now.AddMonths(-1)
            };

            var toDateLabel = new Label
            {
                Text = "إلى تاريخ:",
                Location = new Point(520, 15),
                Size = new Size(60, 20)
            };

            _toDatePicker = new DateTimePicker
            {
                Location = new Point(370, 12),
                Size = new Size(150, 25),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };

            // Status filter
            var statusLabel = new Label
            {
                Text = "الحالة:",
                Location = new Point(300, 15),
                Size = new Size(50, 20)
            };

            _statusComboBox = new ComboBox
            {
                Location = new Point(150, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _statusComboBox.Items.AddRange(new object[]
            {
                new { Text = "الكل", Value = "" },
                new { Text = "مسودة", Value = "draft" },
                new { Text = "معتمد", Value = "approved" },
                new { Text = "معكوس", Value = "reversed" }
            });
            _statusComboBox.DisplayMember = "Text";
            _statusComboBox.ValueMember = "Value";
            _statusComboBox.SelectedIndex = 0;

            var filterButton = new Button
            {
                Text = "فلترة",
                Location = new Point(70, 10),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            filterPanel.Controls.AddRange(new Control[]
            {
                searchLabel, _searchTextBox, fromDateLabel, _fromDatePicker,
                toDateLabel, _toDatePicker, statusLabel, _statusComboBox, filterButton
            });

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            _addButton = new Button
            {
                Text = "إضافة قيد",
                Location = new Point(10, 10),
                Size = new Size(90, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(110, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(200, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _approveButton = new Button
            {
                Text = "اعتماد",
                Location = new Point(290, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(25, 135, 84),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _reverseButton = new Button
            {
                Text = "عكس",
                Location = new Point(380, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(470, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(13, 110, 253),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonsPanel.Controls.AddRange(new Control[]
            {
                _addButton, _editButton, _deleteButton, _approveButton, _reverseButton, _refreshButton
            });

            // Split container for entries and details
            _splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 300
            };

            // Journal entries grid
            _entriesGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            SetupEntriesGridColumns();

            // Details grid
            _detailsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            SetupDetailsGridColumns();

            _splitContainer.Panel1.Controls.Add(_entriesGrid);
            _splitContainer.Panel2.Controls.Add(_detailsGrid);

            this.Controls.AddRange(new Control[] { _splitContainer, buttonsPanel, filterPanel });
        }

        private void SetupEntriesGridColumns()
        {
            if (_entriesGrid == null) return;

            _entriesGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "EntryNumber",
                    HeaderText = "رقم القيد",
                    DataPropertyName = "EntryNumber",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "EntryDate",
                    HeaderText = "التاريخ",
                    DataPropertyName = "EntryDate",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 300
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalDebit",
                    HeaderText = "إجمالي المدين",
                    DataPropertyName = "TotalDebit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalCredit",
                    HeaderText = "إجمالي الدائن",
                    DataPropertyName = "TotalCredit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "StatusDisplay",
                    HeaderText = "الحالة",
                    DataPropertyName = "StatusDisplay",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Reference",
                    HeaderText = "المرجع",
                    DataPropertyName = "Reference",
                    Width = 100
                }
            });
        }

        private void SetupDetailsGridColumns()
        {
            if (_detailsGrid == null) return;

            _detailsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "كود الحساب",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    Width = 250
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Debit",
                    HeaderText = "مدين",
                    DataPropertyName = "Debit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Credit",
                    HeaderText = "دائن",
                    DataPropertyName = "Credit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 200
                }
            });
        }

        private void SetupLayout()
        {
            // Additional layout setup if needed
        }

        private void SetupEvents()
        {
            if (_addButton != null)
                _addButton.Click += AddButton_Click;

            if (_editButton != null)
                _editButton.Click += EditButton_Click;

            if (_deleteButton != null)
                _deleteButton.Click += DeleteButton_Click;

            if (_approveButton != null)
                _approveButton.Click += ApproveButton_Click;

            if (_reverseButton != null)
                _reverseButton.Click += ReverseButton_Click;

            if (_refreshButton != null)
                _refreshButton.Click += RefreshButton_Click;

            if (_entriesGrid != null)
                _entriesGrid.SelectionChanged += EntriesGrid_SelectionChanged;
        }

        private async void LoadJournalEntriesAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;

                var response = await _apiService.GetAsync<List<JournalEntry>>("journal-entries");

                if (response.Success && response.Data != null)
                {
                    _journalEntries = response.Data;
                    PopulateEntriesGrid();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحميل القيود: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PopulateEntriesGrid()
        {
            if (_entriesGrid == null) return;

            _entriesGrid.DataSource = _journalEntries.ToList();
        }

        private void PopulateDetailsGrid(JournalEntry entry)
        {
            if (_detailsGrid == null || entry.Details == null) return;

            _detailsGrid.Rows.Clear();

            foreach (var detail in entry.Details)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_detailsGrid);
                
                row.Cells[0].Value = detail.Account?.Code ?? "";
                row.Cells[1].Value = detail.Account?.Name ?? "";
                row.Cells[2].Value = detail.Debit;
                row.Cells[3].Value = detail.Credit;
                row.Cells[4].Value = detail.Description;

                _detailsGrid.Rows.Add(row);
            }
        }

        // Event handlers
        private void AddButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج إضافة قيد جديد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج تعديل القيد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم تنفيذ عملية الحذف", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ApproveButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم اعتماد القيد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ReverseButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم عكس القيد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            await LoadJournalEntriesAsync();
        }

        private void EntriesGrid_SelectionChanged(object? sender, EventArgs e)
        {
            if (_entriesGrid?.SelectedRows.Count > 0)
            {
                var entry = _entriesGrid.SelectedRows[0].DataBoundItem as JournalEntry;
                if (entry != null)
                {
                    PopulateDetailsGrid(entry);
                    
                    // Update button states
                    var canEdit = entry.Status == "draft";
                    var canApprove = entry.Status == "draft" && entry.IsBalanced;
                    var canReverse = entry.Status == "approved";

                    if (_editButton != null) _editButton.Enabled = canEdit;
                    if (_deleteButton != null) _deleteButton.Enabled = canEdit;
                    if (_approveButton != null) _approveButton.Enabled = canApprove;
                    if (_reverseButton != null) _reverseButton.Enabled = canReverse;
                }
            }
            else
            {
                if (_editButton != null) _editButton.Enabled = false;
                if (_deleteButton != null) _deleteButton.Enabled = false;
                if (_approveButton != null) _approveButton.Enabled = false;
                if (_reverseButton != null) _reverseButton.Enabled = false;
            }
        }
    }
}

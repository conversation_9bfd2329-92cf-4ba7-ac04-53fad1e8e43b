<?php

namespace Database\Seeders;

use App\Models\Role;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إنشاء الصلاحيات
        $permissions = [
            // صلاحيات المستخدمين
            ['name' => 'users.view', 'display_name' => 'عرض المستخدمين', 'module' => 'users', 'action' => 'view'],
            ['name' => 'users.create', 'display_name' => 'إضافة مستخدم', 'module' => 'users', 'action' => 'create'],
            ['name' => 'users.edit', 'display_name' => 'تعديل مستخدم', 'module' => 'users', 'action' => 'edit'],
            ['name' => 'users.delete', 'display_name' => 'حذف مستخدم', 'module' => 'users', 'action' => 'delete'],

            // صلاحيات المحاسبة
            ['name' => 'accounting.view', 'display_name' => 'عرض المحاسبة', 'module' => 'accounting', 'action' => 'view'],
            ['name' => 'accounting.create', 'display_name' => 'إضافة قيود محاسبية', 'module' => 'accounting', 'action' => 'create'],
            ['name' => 'accounting.edit', 'display_name' => 'تعديل قيود محاسبية', 'module' => 'accounting', 'action' => 'edit'],
            ['name' => 'accounting.delete', 'display_name' => 'حذف قيود محاسبية', 'module' => 'accounting', 'action' => 'delete'],
            ['name' => 'accounting.approve', 'display_name' => 'اعتماد قيود محاسبية', 'module' => 'accounting', 'action' => 'approve'],

            // صلاحيات دليل الحسابات
            ['name' => 'accounts.view', 'display_name' => 'عرض دليل الحسابات', 'module' => 'accounts', 'action' => 'view'],
            ['name' => 'accounts.create', 'display_name' => 'إضافة حساب', 'module' => 'accounts', 'action' => 'create'],
            ['name' => 'accounts.edit', 'display_name' => 'تعديل حساب', 'module' => 'accounts', 'action' => 'edit'],
            ['name' => 'accounts.delete', 'display_name' => 'حذف حساب', 'module' => 'accounts', 'action' => 'delete'],

            // صلاحيات المبيعات
            ['name' => 'sales.view', 'display_name' => 'عرض المبيعات', 'module' => 'sales', 'action' => 'view'],
            ['name' => 'sales.create', 'display_name' => 'إضافة فاتورة مبيعات', 'module' => 'sales', 'action' => 'create'],
            ['name' => 'sales.edit', 'display_name' => 'تعديل فاتورة مبيعات', 'module' => 'sales', 'action' => 'edit'],
            ['name' => 'sales.delete', 'display_name' => 'حذف فاتورة مبيعات', 'module' => 'sales', 'action' => 'delete'],
            ['name' => 'sales.approve', 'display_name' => 'اعتماد فاتورة مبيعات', 'module' => 'sales', 'action' => 'approve'],

            // صلاحيات المشتريات
            ['name' => 'purchases.view', 'display_name' => 'عرض المشتريات', 'module' => 'purchases', 'action' => 'view'],
            ['name' => 'purchases.create', 'display_name' => 'إضافة فاتورة مشتريات', 'module' => 'purchases', 'action' => 'create'],
            ['name' => 'purchases.edit', 'display_name' => 'تعديل فاتورة مشتريات', 'module' => 'purchases', 'action' => 'edit'],
            ['name' => 'purchases.delete', 'display_name' => 'حذف فاتورة مشتريات', 'module' => 'purchases', 'action' => 'delete'],
            ['name' => 'purchases.approve', 'display_name' => 'اعتماد فاتورة مشتريات', 'module' => 'purchases', 'action' => 'approve'],

            // صلاحيات المخزون
            ['name' => 'inventory.view', 'display_name' => 'عرض المخزون', 'module' => 'inventory', 'action' => 'view'],
            ['name' => 'inventory.create', 'display_name' => 'إضافة صنف', 'module' => 'inventory', 'action' => 'create'],
            ['name' => 'inventory.edit', 'display_name' => 'تعديل صنف', 'module' => 'inventory', 'action' => 'edit'],
            ['name' => 'inventory.delete', 'display_name' => 'حذف صنف', 'module' => 'inventory', 'action' => 'delete'],
            ['name' => 'inventory.adjust', 'display_name' => 'تعديل المخزون', 'module' => 'inventory', 'action' => 'adjust'],

            // صلاحيات العملاء
            ['name' => 'customers.view', 'display_name' => 'عرض العملاء', 'module' => 'customers', 'action' => 'view'],
            ['name' => 'customers.create', 'display_name' => 'إضافة عميل', 'module' => 'customers', 'action' => 'create'],
            ['name' => 'customers.edit', 'display_name' => 'تعديل عميل', 'module' => 'customers', 'action' => 'edit'],
            ['name' => 'customers.delete', 'display_name' => 'حذف عميل', 'module' => 'customers', 'action' => 'delete'],

            // صلاحيات الموردين
            ['name' => 'suppliers.view', 'display_name' => 'عرض الموردين', 'module' => 'suppliers', 'action' => 'view'],
            ['name' => 'suppliers.create', 'display_name' => 'إضافة مورد', 'module' => 'suppliers', 'action' => 'create'],
            ['name' => 'suppliers.edit', 'display_name' => 'تعديل مورد', 'module' => 'suppliers', 'action' => 'edit'],
            ['name' => 'suppliers.delete', 'display_name' => 'حذف مورد', 'module' => 'suppliers', 'action' => 'delete'],

            // صلاحيات التقارير
            ['name' => 'reports.financial', 'display_name' => 'التقارير المالية', 'module' => 'reports', 'action' => 'view'],
            ['name' => 'reports.sales', 'display_name' => 'تقارير المبيعات', 'module' => 'reports', 'action' => 'view'],
            ['name' => 'reports.purchases', 'display_name' => 'تقارير المشتريات', 'module' => 'reports', 'action' => 'view'],
            ['name' => 'reports.inventory', 'display_name' => 'تقارير المخزون', 'module' => 'reports', 'action' => 'view'],

            // صلاحيات الإعدادات
            ['name' => 'settings.view', 'display_name' => 'عرض الإعدادات', 'module' => 'settings', 'action' => 'view'],
            ['name' => 'settings.edit', 'display_name' => 'تعديل الإعدادات', 'module' => 'settings', 'action' => 'edit'],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }

        // إنشاء الأدوار
        $roles = [
            [
                'name' => 'super_admin',
                'display_name' => 'مدير النظام',
                'description' => 'صلاحيات كاملة على النظام',
                'permissions' => Permission::all()->pluck('name')->toArray(),
            ],
            [
                'name' => 'admin',
                'display_name' => 'مدير',
                'description' => 'صلاحيات إدارية',
                'permissions' => [
                    'users.view', 'users.create', 'users.edit',
                    'accounting.view', 'accounting.create', 'accounting.edit', 'accounting.approve',
                    'accounts.view', 'accounts.create', 'accounts.edit',
                    'sales.view', 'sales.create', 'sales.edit', 'sales.approve',
                    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.approve',
                    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.adjust',
                    'customers.view', 'customers.create', 'customers.edit',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit',
                    'reports.financial', 'reports.sales', 'reports.purchases', 'reports.inventory',
                    'settings.view', 'settings.edit',
                ],
            ],
            [
                'name' => 'accountant',
                'display_name' => 'محاسب',
                'description' => 'صلاحيات المحاسبة',
                'permissions' => [
                    'accounting.view', 'accounting.create', 'accounting.edit',
                    'accounts.view', 'accounts.create', 'accounts.edit',
                    'sales.view', 'purchases.view',
                    'customers.view', 'suppliers.view',
                    'reports.financial', 'reports.sales', 'reports.purchases',
                ],
            ],
            [
                'name' => 'sales_manager',
                'display_name' => 'مدير المبيعات',
                'description' => 'صلاحيات المبيعات والعملاء',
                'permissions' => [
                    'sales.view', 'sales.create', 'sales.edit', 'sales.approve',
                    'customers.view', 'customers.create', 'customers.edit',
                    'inventory.view',
                    'reports.sales',
                ],
            ],
            [
                'name' => 'purchase_manager',
                'display_name' => 'مدير المشتريات',
                'description' => 'صلاحيات المشتريات والموردين',
                'permissions' => [
                    'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.approve',
                    'suppliers.view', 'suppliers.create', 'suppliers.edit',
                    'inventory.view', 'inventory.adjust',
                    'reports.purchases', 'reports.inventory',
                ],
            ],
            [
                'name' => 'inventory_manager',
                'display_name' => 'مدير المخزون',
                'description' => 'صلاحيات إدارة المخزون',
                'permissions' => [
                    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.adjust',
                    'sales.view', 'purchases.view',
                    'reports.inventory',
                ],
            ],
            [
                'name' => 'cashier',
                'display_name' => 'كاشير',
                'description' => 'صلاحيات نقاط البيع',
                'permissions' => [
                    'sales.view', 'sales.create',
                    'customers.view',
                    'inventory.view',
                ],
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(
                ['name' => $roleData['name']],
                [
                    'display_name' => $roleData['display_name'],
                    'description' => $roleData['description'],
                ]
            );

            // ربط الصلاحيات بالدور
            $permissions = Permission::whereIn('name', $roleData['permissions'])->get();
            $role->permissions()->sync($permissions->pluck('id'));
        }

        // إنشاء مستخدم مدير النظام
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'password' => Hash::make('password123'),
                'employee_id' => 'EMP001',
                'department' => 'إدارة النظام',
                'position' => 'مدير النظام',
                'status' => 'active',
            ]
        );

        // ربط مدير النظام بدور super_admin
        $superAdminRole = Role::where('name', 'super_admin')->first();
        $superAdmin->assignRole($superAdminRole);

        $this->command->info('تم إنشاء الأدوار والصلاحيات والمستخدم الافتراضي بنجاح!');
        $this->command->info('بيانات تسجيل الدخول:');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: password123');
    }
}

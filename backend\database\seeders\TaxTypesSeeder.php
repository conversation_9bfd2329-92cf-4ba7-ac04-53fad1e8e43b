<?php

namespace Database\Seeders;

use App\Models\TaxType;
use Illuminate\Database\Seeder;

class TaxTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $taxTypes = [
            [
                'name' => 'ضريبة القيمة المضافة',
                'code' => 'VAT',
                'rate' => 15.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة السعودية 15%',
            ],
            [
                'name' => 'ضريبة القيمة المضافة - مشمولة',
                'code' => 'VAT_INCLUSIVE',
                'rate' => 15.00,
                'type' => 'percentage',
                'calculation_method' => 'inclusive',
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة السعودية 15% مشمولة في السعر',
            ],
            [
                'name' => 'ضريبة انتقائية - التبغ',
                'code' => 'EXCISE_TOBACCO',
                'rate' => 100.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على منتجات التبغ 100%',
            ],
            [
                'name' => 'ضريبة انتقائية - المشروبات الغازية',
                'code' => 'EXCISE_SOFT_DRINKS',
                'rate' => 50.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على المشروبات الغازية 50%',
            ],
            [
                'name' => 'ضريبة انتقائية - مشروبات الطاقة',
                'code' => 'EXCISE_ENERGY_DRINKS',
                'rate' => 100.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على مشروبات الطاقة 100%',
            ],
            [
                'name' => 'معفى من الضريبة',
                'code' => 'TAX_EXEMPT',
                'rate' => 0.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'أصناف معفاة من الضريبة',
            ],
            [
                'name' => 'ضريبة مخصصة 5%',
                'code' => 'CUSTOM_5',
                'rate' => 5.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة مخصصة بمعدل 5%',
            ],
            [
                'name' => 'ضريبة مخصصة 10%',
                'code' => 'CUSTOM_10',
                'rate' => 10.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة مخصصة بمعدل 10%',
            ],
            [
                'name' => 'رسوم ثابتة - 10 ريال',
                'code' => 'FIXED_10',
                'rate' => 10.00,
                'type' => 'fixed',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'رسوم ثابتة 10 ريال',
            ],
            [
                'name' => 'رسوم ثابتة - 50 ريال',
                'code' => 'FIXED_50',
                'rate' => 50.00,
                'type' => 'fixed',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'رسوم ثابتة 50 ريال',
            ],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::firstOrCreate(
                ['code' => $taxType['code']],
                $taxType
            );
        }

        $this->command->info('تم إنشاء أنواع الضرائب بنجاح!');
        $this->command->info('تم إضافة:');
        $this->command->info('- ضريبة القيمة المضافة 15%');
        $this->command->info('- الضرائب الانتقائية');
        $this->command->info('- ضرائب مخصصة');
        $this->command->info('- رسوم ثابتة');
        $this->command->info('- إعفاء ضريبي');
    }
}

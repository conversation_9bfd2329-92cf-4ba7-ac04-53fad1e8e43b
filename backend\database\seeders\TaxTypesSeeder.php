<?php

namespace Database\Seeders;

use App\Models\TaxType;
use App\Models\TaxGroup;
use App\Models\TaxExemption;
use App\Models\TaxBracket;
use App\Models\TaxRule;
use Illuminate\Database\Seeder;

class TaxTypesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $taxTypes = [
            [
                'name' => 'ضريبة القيمة المضافة',
                'code' => 'VAT',
                'rate' => 15.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة السعودية 15%',
            ],
            [
                'name' => 'ضريبة القيمة المضافة - مشمولة',
                'code' => 'VAT_INCLUSIVE',
                'rate' => 15.00,
                'type' => 'percentage',
                'calculation_method' => 'inclusive',
                'is_active' => true,
                'description' => 'ضريبة القيمة المضافة السعودية 15% مشمولة في السعر',
            ],
            [
                'name' => 'ضريبة انتقائية - التبغ',
                'code' => 'EXCISE_TOBACCO',
                'rate' => 100.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على منتجات التبغ 100%',
            ],
            [
                'name' => 'ضريبة انتقائية - المشروبات الغازية',
                'code' => 'EXCISE_SOFT_DRINKS',
                'rate' => 50.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على المشروبات الغازية 50%',
            ],
            [
                'name' => 'ضريبة انتقائية - مشروبات الطاقة',
                'code' => 'EXCISE_ENERGY_DRINKS',
                'rate' => 100.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'الضريبة الانتقائية على مشروبات الطاقة 100%',
            ],
            [
                'name' => 'معفى من الضريبة',
                'code' => 'TAX_EXEMPT',
                'rate' => 0.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'أصناف معفاة من الضريبة',
            ],
            [
                'name' => 'ضريبة مخصصة 5%',
                'code' => 'CUSTOM_5',
                'rate' => 5.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة مخصصة بمعدل 5%',
            ],
            [
                'name' => 'ضريبة مخصصة 10%',
                'code' => 'CUSTOM_10',
                'rate' => 10.00,
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'ضريبة مخصصة بمعدل 10%',
            ],
            [
                'name' => 'رسوم ثابتة - 10 ريال',
                'code' => 'FIXED_10',
                'rate' => 10.00,
                'type' => 'fixed',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'رسوم ثابتة 10 ريال',
            ],
            [
                'name' => 'رسوم ثابتة - 50 ريال',
                'code' => 'FIXED_50',
                'rate' => 50.00,
                'type' => 'fixed',
                'calculation_method' => 'exclusive',
                'is_active' => true,
                'description' => 'رسوم ثابتة 50 ريال',
            ],
        ];

        foreach ($taxTypes as $taxType) {
            TaxType::firstOrCreate(
                ['code' => $taxType['code']],
                $taxType
            );
        }

        // إنشاء المجموعات الضريبية
        $this->createTaxGroups();

        // إنشاء الإعفاءات الضريبية
        $this->createTaxExemptions();

        // إنشاء الشرائح الضريبية
        $this->createTaxBrackets();

        // إنشاء القواعد الضريبية
        $this->createTaxRules();

        $this->command->info('تم إنشاء النظام الضريبي المتقدم بنجاح!');
        $this->command->info('تم إضافة:');
        $this->command->info('- أنواع الضرائب الأساسية');
        $this->command->info('- المجموعات الضريبية');
        $this->command->info('- الإعفاءات الضريبية');
        $this->command->info('- الشرائح الضريبية');
        $this->command->info('- القواعد الضريبية المتقدمة');
    }

    /**
     * إنشاء المجموعات الضريبية
     */
    protected function createTaxGroups()
    {
        $taxGroups = [
            [
                'name' => 'المجموعة الضريبية الأساسية',
                'code' => 'BASIC_TAX_GROUP',
                'description' => 'المجموعة الضريبية الافتراضية للأصناف العادية',
                'applicable_countries' => ['SA', 'AE', 'KW'],
                'effective_from' => '2024-01-01',
            ],
            [
                'name' => 'مجموعة الضرائب الانتقائية',
                'code' => 'EXCISE_TAX_GROUP',
                'description' => 'مجموعة للأصناف الخاضعة للضرائب الانتقائية',
                'applicable_countries' => ['SA'],
                'effective_from' => '2024-01-01',
            ],
            [
                'name' => 'مجموعة الأصناف المعفاة',
                'code' => 'EXEMPT_GROUP',
                'description' => 'مجموعة للأصناف المعفاة من الضريبة',
                'applicable_countries' => ['SA', 'AE', 'KW', 'QA'],
                'effective_from' => '2024-01-01',
            ],
        ];

        foreach ($taxGroups as $groupData) {
            $group = TaxGroup::firstOrCreate(
                ['code' => $groupData['code']],
                $groupData
            );

            // ربط أنواع الضرائب بالمجموعات
            $this->linkTaxTypesToGroups($group);
        }
    }

    /**
     * ربط أنواع الضرائب بالمجموعات
     */
    protected function linkTaxTypesToGroups($group)
    {
        switch ($group->code) {
            case 'BASIC_TAX_GROUP':
                $vatTax = TaxType::where('code', 'VAT')->first();
                if ($vatTax) {
                    $group->taxTypes()->attach($vatTax->id, [
                        'priority' => 1,
                        'is_compound' => false,
                    ]);
                }
                break;

            case 'EXCISE_TAX_GROUP':
                $exciseTaxes = TaxType::whereIn('code', ['EXCISE_TOBACCO', 'EXCISE_SOFT_DRINKS', 'EXCISE_ENERGY_DRINKS'])->get();
                foreach ($exciseTaxes as $index => $tax) {
                    $group->taxTypes()->attach($tax->id, [
                        'priority' => $index + 1,
                        'is_compound' => true, // الضرائب الانتقائية مركبة
                    ]);
                }

                // إضافة ضريبة القيمة المضافة كضريبة مركبة
                $vatTax = TaxType::where('code', 'VAT')->first();
                if ($vatTax) {
                    $group->taxTypes()->attach($vatTax->id, [
                        'priority' => 10,
                        'is_compound' => true,
                    ]);
                }
                break;

            case 'EXEMPT_GROUP':
                $exemptTax = TaxType::where('code', 'TAX_EXEMPT')->first();
                if ($exemptTax) {
                    $group->taxTypes()->attach($exemptTax->id, [
                        'priority' => 1,
                        'is_compound' => false,
                    ]);
                }
                break;
        }
    }

    /**
     * إنشاء الإعفاءات الضريبية
     */
    protected function createTaxExemptions()
    {
        $exemptions = [
            [
                'name' => 'إعفاء الأدوية الأساسية',
                'code' => 'MEDICINE_EXEMPTION',
                'exemption_type' => 'full',
                'description' => 'إعفاء كامل للأدوية الأساسية والمستلزمات الطبية',
                'conditions' => [
                    ['type' => 'item_category', 'value' => 'medicines', 'operator' => '=']
                ],
                'applicable_tax_types' => [1, 2], // VAT و VAT_INCLUSIVE
                'legal_reference' => 'قانون ضريبة القيمة المضافة - المادة 15',
            ],
            [
                'name' => 'إعفاء المواد الغذائية الأساسية',
                'code' => 'BASIC_FOOD_EXEMPTION',
                'exemption_type' => 'full',
                'description' => 'إعفاء كامل للمواد الغذائية الأساسية',
                'conditions' => [
                    ['type' => 'item_category', 'value' => 'basic_food', 'operator' => '=']
                ],
                'applicable_tax_types' => [1, 2],
                'legal_reference' => 'قانون ضريبة القيمة المضافة - المادة 16',
            ],
            [
                'name' => 'إعفاء جزئي للمؤسسات الخيرية',
                'code' => 'CHARITY_EXEMPTION',
                'exemption_type' => 'partial',
                'exemption_percentage' => 50.00,
                'description' => 'إعفاء جزئي 50% للمؤسسات الخيرية',
                'conditions' => [
                    ['type' => 'customer_type', 'value' => 'charity', 'operator' => '=']
                ],
                'applicable_tax_types' => [1, 2],
                'legal_reference' => 'قانون ضريبة القيمة المضافة - المادة 18',
            ],
            [
                'name' => 'إعفاء حسب الحد الأدنى',
                'code' => 'THRESHOLD_EXEMPTION',
                'exemption_type' => 'conditional',
                'description' => 'إعفاء للمبالغ أقل من 1000 ريال',
                'conditions' => [
                    ['type' => 'amount_threshold', 'value' => 1000, 'operator' => '<']
                ],
                'applicable_tax_types' => [1, 2],
            ],
        ];

        foreach ($exemptions as $exemption) {
            TaxExemption::firstOrCreate(
                ['code' => $exemption['code']],
                $exemption
            );
        }
    }

    /**
     * إنشاء الشرائح الضريبية
     */
    protected function createTaxBrackets()
    {
        // إنشاء شرائح ضريبية تصاعدية للضرائب المخصصة
        $customTax = TaxType::where('code', 'CUSTOM_PROGRESSIVE')->first();

        if (!$customTax) {
            $customTax = TaxType::create([
                'name' => 'ضريبة تصاعدية مخصصة',
                'code' => 'CUSTOM_PROGRESSIVE',
                'rate' => 0, // سيتم حسابها من الشرائح
                'type' => 'percentage',
                'calculation_method' => 'exclusive',
                'description' => 'ضريبة تصاعدية بشرائح متعددة',
            ]);
        }

        $brackets = [
            [
                'tax_type_id' => $customTax->id,
                'bracket_name' => 'الشريحة الأولى',
                'min_amount' => 0,
                'max_amount' => 10000,
                'rate' => 5.00,
                'fixed_amount' => 0,
                'calculation_method' => 'progressive',
                'order' => 1,
            ],
            [
                'tax_type_id' => $customTax->id,
                'bracket_name' => 'الشريحة الثانية',
                'min_amount' => 10000,
                'max_amount' => 50000,
                'rate' => 10.00,
                'fixed_amount' => 500, // 5% من 10000
                'calculation_method' => 'progressive',
                'order' => 2,
            ],
            [
                'tax_type_id' => $customTax->id,
                'bracket_name' => 'الشريحة الثالثة',
                'min_amount' => 50000,
                'max_amount' => null, // لا نهاية
                'rate' => 15.00,
                'fixed_amount' => 4500, // 500 + (10% من 40000)
                'calculation_method' => 'progressive',
                'order' => 3,
            ],
        ];

        foreach ($brackets as $bracket) {
            TaxBracket::firstOrCreate(
                [
                    'tax_type_id' => $bracket['tax_type_id'],
                    'bracket_name' => $bracket['bracket_name']
                ],
                $bracket
            );
        }
    }

    /**
     * إنشاء القواعد الضريبية
     */
    protected function createTaxRules()
    {
        $rules = [
            [
                'name' => 'قاعدة الحد الأدنى للضريبة',
                'code' => 'MIN_TAX_RULE',
                'description' => 'تطبيق حد أدنى للضريبة على المعاملات الكبيرة',
                'rule_type' => 'threshold',
                'conditions' => [
                    ['type' => 'amount_threshold', 'value' => 100000, 'operator' => '>=']
                ],
                'actions' => [
                    ['type' => 'set_minimum', 'value' => 1000]
                ],
                'priority' => 1,
                'effective_from' => '2024-01-01',
            ],
            [
                'name' => 'قاعدة الضريبة العكسية للخدمات',
                'code' => 'REVERSE_CHARGE_SERVICES',
                'description' => 'تطبيق الضريبة العكسية على الخدمات المستوردة',
                'rule_type' => 'reverse_charge',
                'conditions' => [
                    ['type' => 'service_type', 'value' => 'imported_service', 'operator' => '='],
                    ['type' => 'supplier_country', 'value' => 'SA', 'operator' => '!=']
                ],
                'actions' => [
                    ['type' => 'reverse_charge', 'value' => true]
                ],
                'priority' => 2,
                'effective_from' => '2024-01-01',
            ],
            [
                'name' => 'قاعدة الضريبة المركبة للكماليات',
                'code' => 'COMPOUND_LUXURY_TAX',
                'description' => 'تطبيق ضرائب مركبة على السلع الكمالية',
                'rule_type' => 'compound',
                'conditions' => [
                    ['type' => 'item_category', 'value' => 'luxury', 'operator' => '=']
                ],
                'actions' => [
                    ['type' => 'multiply_rate', 'value' => 1.5] // زيادة المعدل بـ 50%
                ],
                'priority' => 3,
                'effective_from' => '2024-01-01',
            ],
        ];

        foreach ($rules as $rule) {
            TaxRule::firstOrCreate(
                ['code' => $rule['code']],
                $rule
            );
        }
    }
}

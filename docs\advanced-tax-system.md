# 🧾 النظام الضريبي المتقدم - دليل شامل

## 📋 نظرة عامة

تم تطوير نظام ضريبي متقدم وشامل يدعم جميع أنواع الضرائب والحسابات المعقدة المطلوبة في الأنظمة المحاسبية الحديثة. النظام مصمم ليكون مرناً وقابلاً للتخصيص ومتوافقاً مع اللوائح الضريبية في المملكة العربية السعودية ودول الخليج.

## 🏗️ مكونات النظام

### 1. **أنواع الضرائب (Tax Types)**
- ضريبة القيمة المضافة (15%)
- الضرائب الانتقائية (التبغ، المشروبات الغازية، مشروبات الطاقة)
- ضرائب مخصصة بمعدلات متنوعة
- رسوم ثابتة
- إعفاءات ضريبية

### 2. **المجموعات الضريبية (Tax Groups)**
- تجميع أنواع ضرائب متعددة
- دعم الضرائب المركبة
- أولويات التطبيق
- شروط وحدود التطبيق

### 3. **الإعفاءات الضريبية (Tax Exemptions)**
- إعفاء كامل
- إعفاء جزئي
- إعفاء مشروط
- إعفاءات مؤقتة

### 4. **الشرائح الضريبية (Tax Brackets)**
- ضرائب تصاعدية
- شرائح متعددة
- حسابات معقدة

### 5. **القواعد الضريبية (Tax Rules)**
- قواعد مشروطة
- حدود دنيا وعليا
- ضرائب مركبة
- الضريبة العكسية

### 6. **سجل الحسابات (Tax Calculations)**
- تتبع جميع الحسابات
- تفاصيل كاملة للمراجعة
- سجل المراجعة

### 7. **الإقرارات الضريبية (Tax Returns)**
- إقرارات شهرية/ربعية/سنوية
- حساب تلقائي
- تقارير حكومية

## 🔧 الميزات المتقدمة

### ✅ حساب الضرائب المركبة
```php
// مثال: ضريبة انتقائية + ضريبة قيمة مضافة
$item->tax_group_id = 'EXCISE_TAX_GROUP';
// سيتم حساب:
// 1. الضريبة الانتقائية على السعر الأساسي
// 2. ضريبة القيمة المضافة على (السعر + الضريبة الانتقائية)
```

### ✅ الإعفاءات المشروطة
```php
// إعفاء للمؤسسات الخيرية
$exemption = [
    'type' => 'conditional',
    'conditions' => [
        'customer_type' => 'charity',
        'amount_threshold' => ['operator' => '>', 'value' => 1000]
    ],
    'exemption_percentage' => 50
];
```

### ✅ الضرائب التصاعدية
```php
// شرائح ضريبية متدرجة
$brackets = [
    ['min' => 0, 'max' => 10000, 'rate' => 5],
    ['min' => 10000, 'max' => 50000, 'rate' => 10],
    ['min' => 50000, 'max' => null, 'rate' => 15]
];
```

### ✅ القواعد الذكية
```php
// قاعدة الحد الأدنى للضريبة
$rule = [
    'conditions' => ['amount >= 100000'],
    'actions' => ['set_minimum' => 1000]
];
```

## 📊 أمثلة عملية

### مثال 1: حساب ضريبة صنف عادي
```php
$item = Item::find(1);
$item->is_taxable = true;
$item->tax_type_id = 1; // VAT 15%

$calculation = $taxService->calculateAdvancedLineItemTax(
    $item, 
    $quantity = 2, 
    $unitPrice = 100, 
    $discount = 10
);

// النتيجة:
// net_amount: 190 (2×100 - 10)
// tax_amount: 28.5 (190 × 15%)
// total_amount: 218.5
```

### مثال 2: حساب ضريبة مركبة
```php
$item = Item::find(2);
$item->tax_group_id = 2; // EXCISE_TAX_GROUP

// المجموعة تحتوي على:
// 1. ضريبة انتقائية 100% (أولوية 1)
// 2. ضريبة قيمة مضافة 15% (أولوية 2، مركبة)

$calculation = $taxService->calculateAdvancedLineItemTax(
    $item, 
    $quantity = 1, 
    $unitPrice = 100
);

// النتيجة:
// base_amount: 100
// excise_tax: 100 (100 × 100%)
// vat_base: 200 (100 + 100)
// vat_tax: 30 (200 × 15%)
// total_tax: 130
// total_amount: 230
```

### مثال 3: تطبيق إعفاء
```php
$item = Item::find(3);
$item->is_taxable = true;
$item->tax_type_id = 1; // VAT 15%
$item->tax_exemptions = [1]; // إعفاء الأدوية

$calculation = $taxService->calculateAdvancedLineItemTax(
    $item, 
    $quantity = 1, 
    $unitPrice = 100,
    $discount = 0,
    $context = ['item_category' => 'medicines']
);

// النتيجة:
// net_amount: 100
// calculated_tax: 15
// exemption_amount: 15 (إعفاء كامل)
// final_tax: 0
// total_amount: 100
```

## 🔗 API Endpoints

### أنواع الضرائب
```
GET    /api/tax-types              # قائمة أنواع الضرائب
POST   /api/tax-types              # إضافة نوع ضريبة جديد
GET    /api/tax-types/{id}         # تفاصيل نوع ضريبة
PUT    /api/tax-types/{id}         # تحديث نوع ضريبة
DELETE /api/tax-types/{id}         # حذف نوع ضريبة
POST   /api/tax-types/calculate    # حساب الضريبة
```

### المجموعات الضريبية
```
GET    /api/tax-groups             # قائمة المجموعات الضريبية
POST   /api/tax-groups             # إضافة مجموعة جديدة
GET    /api/tax-groups/{id}        # تفاصيل مجموعة
PUT    /api/tax-groups/{id}        # تحديث مجموعة
DELETE /api/tax-groups/{id}        # حذف مجموعة
```

### الإعفاءات الضريبية
```
GET    /api/tax-exemptions         # قائمة الإعفاءات
POST   /api/tax-exemptions         # إضافة إعفاء جديد
GET    /api/tax-exemptions/{id}    # تفاصيل إعفاء
PUT    /api/tax-exemptions/{id}    # تحديث إعفاء
DELETE /api/tax-exemptions/{id}    # حذف إعفاء
```

### التقارير الضريبية
```
GET    /api/tax-reports/vat        # تقرير ضريبة القيمة المضافة
GET    /api/tax-reports/excise     # تقرير الضرائب الانتقائية
GET    /api/tax-reports/exemptions # تقرير الإعفاءات
POST   /api/tax-reports/returns    # إنشاء إقرار ضريبي
```

## 📈 التقارير المتاحة

### 1. تقرير ضريبة القيمة المضافة
- إجمالي المبيعات
- المبيعات الخاضعة للضريبة
- المبيعات المعفاة
- ضريبة المخرجات
- ضريبة المدخلات
- صافي الضريبة المستحقة

### 2. تقرير الضرائب الانتقائية
- مبيعات التبغ ومنتجاته
- مبيعات المشروبات الغازية
- مبيعات مشروبات الطاقة
- إجمالي الضرائب الانتقائية

### 3. تقرير الإعفاءات
- قيمة الإعفاءات المطبقة
- توفير ضريبي للعملاء
- تفصيل حسب نوع الإعفاء

### 4. الإقرارات الضريبية
- إقرارات شهرية/ربعية/سنوية
- حساب تلقائي للمستحقات
- تصدير بصيغ مختلفة

## 🔒 الأمان والمراجعة

### سجل المراجعة
- تسجيل جميع العمليات الضريبية
- تتبع التغييرات
- سجل المستخدمين والأوقات

### التحقق من الصحة
- التحقق من توازن الحسابات
- التحقق من صحة المعدلات
- التحقق من الشروط والقيود

### الصلاحيات
- صلاحيات منفصلة لكل وحدة
- حماية العمليات الحساسة
- مراجعة وموافقة الإقرارات

## 🌍 التوافق مع اللوائح

### المملكة العربية السعودية
- ضريبة القيمة المضافة 15%
- الضرائب الانتقائية حسب اللوائح
- متطلبات الإقرارات الحكومية

### دول الخليج
- مرونة في المعدلات
- دعم العملات المختلفة
- تخصيص حسب كل دولة

هذا النظام الضريبي المتقدم يوفر حلولاً شاملة لجميع احتياجات المحاسبة الضريبية في الشركات الحديثة.

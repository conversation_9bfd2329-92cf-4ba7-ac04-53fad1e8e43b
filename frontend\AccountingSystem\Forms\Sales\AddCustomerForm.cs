using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Sales
{
    public partial class AddCustomerForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;
        
        // Customer info controls
        private TextBox? _nameTextBox;
        private TextBox? _nameEnTextBox;
        private TextBox? _phoneTextBox;
        private TextBox? _emailTextBox;
        private TextBox? _addressTextBox;
        private TextBox? _taxNumberTextBox;
        private ComboBox? _customerTypeComboBox;
        private NumericUpDown? _creditLimitNumeric;
        private NumericUpDown? _paymentTermsNumeric;
        private NumericUpDown? _openingBalanceNumeric;
        private TextBox? _notesTextBox;
        
        // Account linking controls
        private CheckBox? _createAccountCheckBox;
        private ComboBox? _parentAccountComboBox;
        private TextBox? _accountCodeTextBox;
        private TextBox? _accountNameTextBox;
        
        // Action buttons
        private Button? _saveButton;
        private Button? _cancelButton;
        
        // Data
        private List<Account> _accounts = new();
        private Customer? _customer;
        private bool _isEditMode;

        public Customer? Customer => _customer;

        public AddCustomerForm(IApiService apiService, IAuthService authService, Customer? customer = null)
        {
            _apiService = apiService;
            _authService = authService;
            _customer = customer;
            _isEditMode = customer != null;
            
            InitializeComponent();
            SetupForm();
            LoadAccountsAsync();
            
            if (_isEditMode)
                PopulateCustomerData();
        }

        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل عميل" : "إضافة عميل جديد";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.White;

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Main panel with scroll
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                Padding = new Padding(20)
            };

            // Customer Information Group
            var customerInfoGroup = new GroupBox
            {
                Text = "معلومات العميل",
                Location = new Point(20, 20),
                Size = new Size(540, 350),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            // Name (Arabic)
            var nameLabel = new Label
            {
                Text = "اسم العميل (عربي) *:",
                Location = new Point(20, 30),
                Size = new Size(150, 20)
            };

            _nameTextBox = new TextBox
            {
                Location = new Point(20, 55),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Name (English)
            var nameEnLabel = new Label
            {
                Text = "اسم العميل (إنجليزي):",
                Location = new Point(280, 30),
                Size = new Size(150, 20)
            };

            _nameEnTextBox = new TextBox
            {
                Location = new Point(280, 55),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Customer Type
            var customerTypeLabel = new Label
            {
                Text = "نوع العميل *:",
                Location = new Point(20, 90),
                Size = new Size(100, 20)
            };

            _customerTypeComboBox = new ComboBox
            {
                Location = new Point(20, 115),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _customerTypeComboBox.Items.AddRange(new object[]
            {
                new { Text = "فرد", Value = "individual" },
                new { Text = "شركة", Value = "company" }
            });
            _customerTypeComboBox.DisplayMember = "Text";
            _customerTypeComboBox.ValueMember = "Value";
            _customerTypeComboBox.SelectedIndex = 0;

            // Phone
            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(200, 90),
                Size = new Size(100, 20)
            };

            _phoneTextBox = new TextBox
            {
                Location = new Point(200, 115),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Email
            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Location = new Point(370, 90),
                Size = new Size(120, 20)
            };

            _emailTextBox = new TextBox
            {
                Location = new Point(370, 115),
                Size = new Size(160, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Address
            var addressLabel = new Label
            {
                Text = "العنوان:",
                Location = new Point(20, 150),
                Size = new Size(100, 20)
            };

            _addressTextBox = new TextBox
            {
                Location = new Point(20, 175),
                Size = new Size(510, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Tax Number
            var taxNumberLabel = new Label
            {
                Text = "الرقم الضريبي:",
                Location = new Point(20, 210),
                Size = new Size(100, 20)
            };

            _taxNumberTextBox = new TextBox
            {
                Location = new Point(20, 235),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Credit Limit
            var creditLimitLabel = new Label
            {
                Text = "الحد الائتماني:",
                Location = new Point(240, 210),
                Size = new Size(100, 20)
            };

            _creditLimitNumeric = new NumericUpDown
            {
                Location = new Point(240, 235),
                Size = new Size(120, 25),
                Minimum = 0,
                Maximum = 9999999,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };

            // Payment Terms
            var paymentTermsLabel = new Label
            {
                Text = "مدة السداد (يوم):",
                Location = new Point(380, 210),
                Size = new Size(120, 20)
            };

            _paymentTermsNumeric = new NumericUpDown
            {
                Location = new Point(380, 235),
                Size = new Size(80, 25),
                Minimum = 0,
                Maximum = 365,
                Value = 30
            };

            // Opening Balance
            var openingBalanceLabel = new Label
            {
                Text = "الرصيد الافتتاحي:",
                Location = new Point(20, 270),
                Size = new Size(120, 20)
            };

            _openingBalanceNumeric = new NumericUpDown
            {
                Location = new Point(20, 295),
                Size = new Size(150, 25),
                Minimum = -9999999,
                Maximum = 9999999,
                DecimalPlaces = 2,
                ThousandsSeparator = true
            };

            // Notes
            var notesLabel = new Label
            {
                Text = "ملاحظات:",
                Location = new Point(200, 270),
                Size = new Size(100, 20)
            };

            _notesTextBox = new TextBox
            {
                Location = new Point(200, 295),
                Size = new Size(330, 25),
                Font = new Font("Segoe UI", 10)
            };

            customerInfoGroup.Controls.AddRange(new Control[]
            {
                nameLabel, _nameTextBox, nameEnLabel, _nameEnTextBox,
                customerTypeLabel, _customerTypeComboBox, phoneLabel, _phoneTextBox,
                emailLabel, _emailTextBox, addressLabel, _addressTextBox,
                taxNumberLabel, _taxNumberTextBox, creditLimitLabel, _creditLimitNumeric,
                paymentTermsLabel, _paymentTermsNumeric, openingBalanceLabel, _openingBalanceNumeric,
                notesLabel, _notesTextBox
            });

            // Account Linking Group
            var accountLinkingGroup = new GroupBox
            {
                Text = "ربط الحساب المحاسبي",
                Location = new Point(20, 380),
                Size = new Size(540, 200),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _createAccountCheckBox = new CheckBox
            {
                Text = "إنشاء حساب محاسبي للعميل",
                Location = new Point(20, 30),
                Size = new Size(200, 25),
                Checked = true,
                Font = new Font("Segoe UI", 10)
            };

            var parentAccountLabel = new Label
            {
                Text = "الحساب الأب:",
                Location = new Point(20, 65),
                Size = new Size(100, 20)
            };

            _parentAccountComboBox = new ComboBox
            {
                Location = new Point(20, 90),
                Size = new Size(250, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };

            var accountCodeLabel = new Label
            {
                Text = "كود الحساب:",
                Location = new Point(290, 65),
                Size = new Size(100, 20)
            };

            _accountCodeTextBox = new TextBox
            {
                Location = new Point(290, 90),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10),
                ReadOnly = true,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var accountNameLabel = new Label
            {
                Text = "اسم الحساب:",
                Location = new Point(20, 125),
                Size = new Size(100, 20)
            };

            _accountNameTextBox = new TextBox
            {
                Location = new Point(20, 150),
                Size = new Size(370, 25),
                Font = new Font("Segoe UI", 10)
            };

            accountLinkingGroup.Controls.AddRange(new Control[]
            {
                _createAccountCheckBox, parentAccountLabel, _parentAccountComboBox,
                accountCodeLabel, _accountCodeTextBox, accountNameLabel, _accountNameTextBox
            });

            // Buttons Panel
            var buttonsPanel = new Panel
            {
                Location = new Point(20, 590),
                Size = new Size(540, 50)
            };

            _saveButton = new Button
            {
                Text = _isEditMode ? "حفظ التعديلات" : "حفظ",
                Location = new Point(350, 10),
                Size = new Size(120, 35),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _cancelButton = new Button
            {
                Text = "إلغاء",
                Location = new Point(480, 10),
                Size = new Size(80, 35),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10)
            };

            buttonsPanel.Controls.AddRange(new Control[] { _saveButton, _cancelButton });

            mainPanel.Controls.AddRange(new Control[] 
            { 
                customerInfoGroup, accountLinkingGroup, buttonsPanel 
            });

            this.Controls.Add(mainPanel);
        }

        private void SetupLayout()
        {
            // Set tab order
            var controls = new Control[]
            {
                _nameTextBox!, _nameEnTextBox!, _customerTypeComboBox!, _phoneTextBox!,
                _emailTextBox!, _addressTextBox!, _taxNumberTextBox!, _creditLimitNumeric!,
                _paymentTermsNumeric!, _openingBalanceNumeric!, _notesTextBox!,
                _createAccountCheckBox!, _parentAccountComboBox!, _accountNameTextBox!,
                _saveButton!, _cancelButton!
            };

            for (int i = 0; i < controls.Length; i++)
            {
                controls[i].TabIndex = i;
            }

            this.AcceptButton = _saveButton;
            this.CancelButton = _cancelButton;
        }

        private void SetupEvents()
        {
            if (_saveButton != null)
                _saveButton.Click += SaveButton_Click;

            if (_cancelButton != null)
                _cancelButton.Click += CancelButton_Click;

            if (_createAccountCheckBox != null)
                _createAccountCheckBox.CheckedChanged += CreateAccountCheckBox_CheckedChanged;

            if (_nameTextBox != null)
                _nameTextBox.TextChanged += NameTextBox_TextChanged;

            if (_parentAccountComboBox != null)
                _parentAccountComboBox.SelectedIndexChanged += ParentAccountComboBox_SelectedIndexChanged;
        }

        private async void LoadAccountsAsync()
        {
            try
            {
                var response = await _apiService.GetAsync<List<Account>>("accounts?account_type=asset");
                if (response.Success && response.Data != null)
                {
                    _accounts = response.Data.Where(a => a.AccountType == "asset" && a.Name.Contains("عملاء")).ToList();
                    PopulateParentAccountsComboBox();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateParentAccountsComboBox()
        {
            if (_parentAccountComboBox == null) return;

            _parentAccountComboBox.Items.Clear();
            
            foreach (var account in _accounts)
            {
                _parentAccountComboBox.Items.Add(new { Text = account.DisplayName, Value = account.Id });
            }

            _parentAccountComboBox.DisplayMember = "Text";
            _parentAccountComboBox.ValueMember = "Value";

            if (_parentAccountComboBox.Items.Count > 0)
                _parentAccountComboBox.SelectedIndex = 0;
        }

        private void PopulateCustomerData()
        {
            if (_customer == null) return;

            _nameTextBox!.Text = _customer.Name;
            _nameEnTextBox!.Text = _customer.NameEn;
            _phoneTextBox!.Text = _customer.Phone;
            _emailTextBox!.Text = _customer.Email;
            _addressTextBox!.Text = _customer.Address;
            _taxNumberTextBox!.Text = _customer.TaxNumber;
            _creditLimitNumeric!.Value = _customer.CreditLimit;
            _paymentTermsNumeric!.Value = _customer.PaymentTerms;
            _openingBalanceNumeric!.Value = _customer.OpeningBalance;
            _notesTextBox!.Text = _customer.Notes;

            // Set customer type
            for (int i = 0; i < _customerTypeComboBox!.Items.Count; i++)
            {
                var item = _customerTypeComboBox.Items[i] as dynamic;
                if (item?.Value == _customer.CustomerType)
                {
                    _customerTypeComboBox.SelectedIndex = i;
                    break;
                }
            }
        }

        // Event handlers
        private void CreateAccountCheckBox_CheckedChanged(object? sender, EventArgs e)
        {
            var enabled = _createAccountCheckBox?.Checked ?? false;
            
            if (_parentAccountComboBox != null) _parentAccountComboBox.Enabled = enabled;
            if (_accountCodeTextBox != null) _accountCodeTextBox.Enabled = enabled;
            if (_accountNameTextBox != null) _accountNameTextBox.Enabled = enabled;

            if (enabled)
                GenerateAccountCode();
        }

        private void NameTextBox_TextChanged(object? sender, EventArgs e)
        {
            if (_accountNameTextBox != null && _createAccountCheckBox?.Checked == true)
            {
                _accountNameTextBox.Text = _nameTextBox?.Text ?? "";
            }
        }

        private void ParentAccountComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_createAccountCheckBox?.Checked == true)
                GenerateAccountCode();
        }

        private void GenerateAccountCode()
        {
            if (_parentAccountComboBox?.SelectedItem == null || _accountCodeTextBox == null)
                return;

            var parentAccountId = ((dynamic)_parentAccountComboBox.SelectedItem).Value;
            var parentAccount = _accounts.FirstOrDefault(a => a.Id == parentAccountId);

            if (parentAccount != null)
            {
                // Generate next available code
                var baseCode = parentAccount.Code;
                var nextNumber = 1;
                
                // Find next available number
                var existingCodes = _accounts
                    .Where(a => a.Code.StartsWith(baseCode) && a.Code.Length == baseCode.Length + 2)
                    .Select(a => a.Code.Substring(baseCode.Length))
                    .Where(suffix => int.TryParse(suffix, out _))
                    .Select(int.Parse)
                    .ToList();

                if (existingCodes.Any())
                    nextNumber = existingCodes.Max() + 1;

                _accountCodeTextBox.Text = baseCode + nextNumber.ToString("00");
            }
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            if (!ValidateForm())
                return;

            try
            {
                this.Cursor = Cursors.WaitCursor;
                _saveButton!.Enabled = false;
                _saveButton.Text = "جاري الحفظ...";

                var customerData = new
                {
                    name = _nameTextBox!.Text.Trim(),
                    name_en = _nameEnTextBox!.Text.Trim(),
                    phone = _phoneTextBox!.Text.Trim(),
                    email = _emailTextBox!.Text.Trim(),
                    address = _addressTextBox!.Text.Trim(),
                    tax_number = _taxNumberTextBox!.Text.Trim(),
                    customer_type = ((dynamic)_customerTypeComboBox!.SelectedItem).Value,
                    credit_limit = _creditLimitNumeric!.Value,
                    payment_terms = (int)_paymentTermsNumeric!.Value,
                    opening_balance = _openingBalanceNumeric!.Value,
                    notes = _notesTextBox!.Text.Trim(),
                    create_account = _createAccountCheckBox!.Checked,
                    parent_account_id = _createAccountCheckBox.Checked ? 
                        ((dynamic)_parentAccountComboBox!.SelectedItem)?.Value : null,
                    account_code = _createAccountCheckBox.Checked ? _accountCodeTextBox!.Text : null,
                    account_name = _createAccountCheckBox.Checked ? _accountNameTextBox!.Text : null
                };

                ApiResponse<Customer> response;
                if (_isEditMode)
                {
                    response = await _apiService.PutAsync<Customer>($"customers/{_customer!.Id}", customerData);
                }
                else
                {
                    response = await _apiService.PostAsync<Customer>("customers", customerData);
                }

                if (response.Success && response.Data != null)
                {
                    _customer = response.Data;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الحفظ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                _saveButton!.Enabled = true;
                _saveButton.Text = _isEditMode ? "حفظ التعديلات" : "حفظ";
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateForm()
        {
            if (string.IsNullOrWhiteSpace(_nameTextBox?.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _nameTextBox?.Focus();
                return false;
            }

            if (_customerTypeComboBox?.SelectedIndex < 0)
            {
                MessageBox.Show("يرجى اختيار نوع العميل", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _customerTypeComboBox?.Focus();
                return false;
            }

            if (_createAccountCheckBox?.Checked == true)
            {
                if (_parentAccountComboBox?.SelectedIndex < 0)
                {
                    MessageBox.Show("يرجى اختيار الحساب الأب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _parentAccountComboBox?.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(_accountNameTextBox?.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الحساب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    _accountNameTextBox?.Focus();
                    return false;
                }
            }

            return true;
        }
    }

    // Helper class for invoice items in POS
    public class InvoiceItem
    {
        public int ItemId { get; set; }
        public string ItemCode { get; set; } = string.Empty;
        public string ItemName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }
    }
}

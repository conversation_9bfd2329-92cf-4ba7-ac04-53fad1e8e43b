<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class JournalEntry extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'entry_number',
        'entry_date',
        'description',
        'reference',
        'total_debit',
        'total_credit',
        'status',
        'user_id',
        'approved_by',
        'approved_at',
        'branch_id',
        'attachments',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'entry_date' => 'date',
        'total_debit' => 'decimal:2',
        'total_credit' => 'decimal:2',
        'approved_at' => 'datetime',
        'attachments' => 'array',
    ];

    /**
     * Get the user who created this entry.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who approved this entry.
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the branch for this entry.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the details for this journal entry.
     */
    public function details()
    {
        return $this->hasMany(JournalEntryDetail::class);
    }

    /**
     * Scope a query to only include approved entries.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', 'approved');
    }

    /**
     * Scope a query to only include draft entries.
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeDateRange($query, $from, $to)
    {
        return $query->whereBetween('entry_date', [$from, $to]);
    }

    /**
     * Check if entry is balanced.
     */
    public function isBalanced()
    {
        return $this->total_debit == $this->total_credit;
    }

    /**
     * Approve the journal entry.
     */
    public function approve($approvedBy = null)
    {
        if (!$this->isBalanced()) {
            throw new \Exception('لا يمكن اعتماد قيد غير متوازن');
        }

        $this->update([
            'status' => 'approved',
            'approved_by' => $approvedBy ?? auth()->id(),
            'approved_at' => now(),
        ]);

        // تحديث أرصدة الحسابات
        foreach ($this->details as $detail) {
            $detail->account->updateBalance($detail->debit, 'debit');
            $detail->account->updateBalance($detail->credit, 'credit');
        }
    }

    /**
     * Reverse the journal entry.
     */
    public function reverse($description = null)
    {
        if ($this->status !== 'approved') {
            throw new \Exception('لا يمكن عكس قيد غير معتمد');
        }

        // إنشاء قيد عكسي
        $reverseEntry = static::create([
            'entry_number' => $this->generateEntryNumber(),
            'entry_date' => now()->toDateString(),
            'description' => $description ?? 'عكس القيد رقم ' . $this->entry_number,
            'reference' => 'REV-' . $this->entry_number,
            'total_debit' => $this->total_credit,
            'total_credit' => $this->total_debit,
            'status' => 'approved',
            'user_id' => auth()->id(),
            'approved_by' => auth()->id(),
            'approved_at' => now(),
            'branch_id' => $this->branch_id,
        ]);

        // إنشاء تفاصيل القيد العكسي
        foreach ($this->details as $detail) {
            $reverseEntry->details()->create([
                'account_id' => $detail->account_id,
                'cost_center_id' => $detail->cost_center_id,
                'debit' => $detail->credit,
                'credit' => $detail->debit,
                'description' => 'عكس: ' . $detail->description,
            ]);

            // تحديث أرصدة الحسابات
            $detail->account->updateBalance($detail->credit, 'debit');
            $detail->account->updateBalance($detail->debit, 'credit');
        }

        // تحديث حالة القيد الأصلي
        $this->update(['status' => 'reversed']);

        return $reverseEntry;
    }

    /**
     * Generate unique entry number.
     */
    public static function generateEntryNumber()
    {
        $year = date('Y');
        $lastEntry = static::where('entry_number', 'like', "JE-{$year}-%")
                           ->orderBy('entry_number', 'desc')
                           ->first();

        if ($lastEntry) {
            $lastNumber = (int) substr($lastEntry->entry_number, -6);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "JE-{$year}-" . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals from details.
     */
    public function calculateTotals()
    {
        $totalDebit = $this->details()->sum('debit');
        $totalCredit = $this->details()->sum('credit');

        $this->update([
            'total_debit' => $totalDebit,
            'total_credit' => $totalCredit,
        ]);
    }
}

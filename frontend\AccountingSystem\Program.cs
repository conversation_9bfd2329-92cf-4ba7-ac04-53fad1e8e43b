using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using AccountingSystem.Services;
using AccountingSystem.Forms;

namespace AccountingSystem
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // Setup dependency injection
            var services = new ServiceCollection();
            ConfigureServices(services);

            var serviceProvider = services.BuildServiceProvider();

            try
            {
                // Get the auth service
                var authService = serviceProvider.GetRequiredService<IAuthService>();

                // Check if user is already logged in
                if (authService.IsAuthenticated)
                {
                    // Show main form directly
                    var mainForm = serviceProvider.GetRequiredService<MainForm>();
                    Application.Run(mainForm);
                }
                else
                {
                    // Show login form first
                    var loginForm = serviceProvider.GetRequiredService<LoginForm>();
                    if (loginForm.ShowDialog() == DialogResult.OK)
                    {
                        // Login successful, show main form
                        var mainForm = serviceProvider.GetRequiredService<MainForm>();
                        Application.Run(mainForm);
                    }
                    // If login cancelled or failed, application will exit
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تشغيل التطبيق:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                serviceProvider.Dispose();
            }
        }

        private static void ConfigureServices(ServiceCollection services)
        {
            // Configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // Logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddDebug();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Services
            services.AddSingleton<IApiService, ApiService>();
            services.AddSingleton<IAuthService, AuthService>();

            // Forms
            services.AddTransient<LoginForm>();
            services.AddTransient<MainForm>();
        }
    }
}

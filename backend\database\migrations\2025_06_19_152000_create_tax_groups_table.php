<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المجموعة الضريبية
            $table->string('code')->unique(); // رمز المجموعة
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('applicable_countries')->nullable(); // الدول المطبقة
            $table->date('effective_from')->nullable(); // تاريخ السريان
            $table->date('effective_to')->nullable(); // تاريخ الانتهاء
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_groups');
    }
};

<?php

namespace App\Http\Controllers;

use App\Models\TaxReturn;
use App\Models\TaxCalculation;
use App\Models\SalesInvoice;
use App\Models\PurchaseInvoice;
use App\Services\AdvancedTaxCalculationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TaxReportController extends Controller
{
    protected $taxCalculationService;

    public function __construct(AdvancedTaxCalculationService $taxCalculationService)
    {
        $this->taxCalculationService = $taxCalculationService;
    }

    /**
     * تقرير ضريبة القيمة المضافة
     */
    public function vatReport(Request $request)
    {
        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $periodStart = Carbon::parse($request->period_start);
        $periodEnd = Carbon::parse($request->period_end);

        // مبيعات خاضعة للضريبة
        $taxableSales = $this->getTaxableSales($periodStart, $periodEnd, $request->branch_id);
        
        // مبيعات معفاة
        $exemptSales = $this->getExemptSales($periodStart, $periodEnd, $request->branch_id);
        
        // ضريبة المخرجات
        $outputTax = $this->getOutputTax($periodStart, $periodEnd, $request->branch_id);
        
        // ضريبة المدخلات
        $inputTax = $this->getInputTax($periodStart, $periodEnd, $request->branch_id);
        
        // صافي الضريبة
        $netTax = $outputTax - $inputTax;

        return response()->json([
            'success' => true,
            'data' => [
                'period' => [
                    'start' => $periodStart->format('Y-m-d'),
                    'end' => $periodEnd->format('Y-m-d'),
                ],
                'summary' => [
                    'total_sales' => $taxableSales + $exemptSales,
                    'taxable_sales' => $taxableSales,
                    'exempt_sales' => $exemptSales,
                    'output_tax' => $outputTax,
                    'input_tax' => $inputTax,
                    'net_tax' => $netTax,
                    'tax_rate' => 15.00, // معدل ضريبة القيمة المضافة
                ],
                'breakdown' => $this->getVATBreakdown($periodStart, $periodEnd, $request->branch_id),
            ],
        ]);
    }

    /**
     * تقرير الضرائب الانتقائية
     */
    public function exciseTaxReport(Request $request)
    {
        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
            'tax_type' => 'nullable|in:tobacco,soft_drinks,energy_drinks',
        ]);

        $periodStart = Carbon::parse($request->period_start);
        $periodEnd = Carbon::parse($request->period_end);

        $exciseTaxData = $this->getExciseTaxData($periodStart, $periodEnd, $request->tax_type);

        return response()->json([
            'success' => true,
            'data' => [
                'period' => [
                    'start' => $periodStart->format('Y-m-d'),
                    'end' => $periodEnd->format('Y-m-d'),
                ],
                'tax_type' => $request->tax_type ?? 'all',
                'summary' => $exciseTaxData['summary'],
                'breakdown' => $exciseTaxData['breakdown'],
            ],
        ]);
    }

    /**
     * تقرير الإعفاءات الضريبية
     */
    public function taxExemptionReport(Request $request)
    {
        $request->validate([
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
        ]);

        $periodStart = Carbon::parse($request->period_start);
        $periodEnd = Carbon::parse($request->period_end);

        $exemptionData = $this->getTaxExemptionData($periodStart, $periodEnd);

        return response()->json([
            'success' => true,
            'data' => [
                'period' => [
                    'start' => $periodStart->format('Y-m-d'),
                    'end' => $periodEnd->format('Y-m-d'),
                ],
                'summary' => $exemptionData['summary'],
                'exemptions' => $exemptionData['exemptions'],
                'savings' => $exemptionData['savings'],
            ],
        ]);
    }

    /**
     * إنشاء إقرار ضريبي
     */
    public function createTaxReturn(Request $request)
    {
        $request->validate([
            'return_type' => 'required|in:vat,withholding,excise,custom',
            'period_type' => 'required|in:monthly,quarterly,annual',
            'period_start' => 'required|date',
            'period_end' => 'required|date|after_or_equal:period_start',
        ]);

        $periodStart = Carbon::parse($request->period_start);
        $periodEnd = Carbon::parse($request->period_end);

        // حساب بيانات الإقرار
        $returnData = $this->calculateTaxReturnData($request->return_type, $periodStart, $periodEnd);

        // إنشاء رقم الإقرار
        $returnNumber = $this->generateReturnNumber($request->return_type, $periodStart);

        // حساب تاريخ الاستحقاق
        $dueDate = $this->calculateDueDate($request->period_type, $periodEnd);

        $taxReturn = TaxReturn::create([
            'return_number' => $returnNumber,
            'return_type' => $request->return_type,
            'period_type' => $request->period_type,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'due_date' => $dueDate,
            'status' => 'calculated',
            'total_sales' => $returnData['total_sales'],
            'taxable_sales' => $returnData['taxable_sales'],
            'exempt_sales' => $returnData['exempt_sales'],
            'output_tax' => $returnData['output_tax'],
            'input_tax' => $returnData['input_tax'],
            'net_tax' => $returnData['net_tax'],
            'total_due' => $returnData['total_due'],
            'detailed_breakdown' => $returnData['breakdown'],
            'prepared_by' => auth()->id(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الإقرار الضريبي بنجاح',
            'data' => $taxReturn,
        ], 201);
    }

    /**
     * الحصول على المبيعات الخاضعة للضريبة
     */
    protected function getTaxableSales($periodStart, $periodEnd, $branchId = null)
    {
        $query = SalesInvoice::whereBetween('invoice_date', [$periodStart, $periodEnd])
                            ->where('status', 'approved');

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->sum('subtotal') - $query->sum('discount_amount');
    }

    /**
     * الحصول على المبيعات المعفاة
     */
    protected function getExemptSales($periodStart, $periodEnd, $branchId = null)
    {
        // هذا يحتاج إلى تطوير أكثر بناءً على كيفية تتبع المبيعات المعفاة
        return 0;
    }

    /**
     * الحصول على ضريبة المخرجات
     */
    protected function getOutputTax($periodStart, $periodEnd, $branchId = null)
    {
        $query = SalesInvoice::whereBetween('invoice_date', [$periodStart, $periodEnd])
                            ->where('status', 'approved');

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->sum('tax_amount');
    }

    /**
     * الحصول على ضريبة المدخلات
     */
    protected function getInputTax($periodStart, $periodEnd, $branchId = null)
    {
        $query = PurchaseInvoice::whereBetween('invoice_date', [$periodStart, $periodEnd])
                               ->where('status', 'approved');

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        return $query->sum('tax_amount');
    }

    /**
     * الحصول على تفصيل ضريبة القيمة المضافة
     */
    protected function getVATBreakdown($periodStart, $periodEnd, $branchId = null)
    {
        // تفصيل حسب نوع الضريبة ومعدلها
        return [
            'standard_rate' => [
                'rate' => 15.00,
                'taxable_amount' => 0,
                'tax_amount' => 0,
            ],
            'zero_rate' => [
                'rate' => 0.00,
                'taxable_amount' => 0,
                'tax_amount' => 0,
            ],
            'exempt' => [
                'amount' => 0,
            ],
        ];
    }

    /**
     * الحصول على بيانات الضرائب الانتقائية
     */
    protected function getExciseTaxData($periodStart, $periodEnd, $taxType = null)
    {
        return [
            'summary' => [
                'total_excise_sales' => 0,
                'total_excise_tax' => 0,
            ],
            'breakdown' => [],
        ];
    }

    /**
     * الحصول على بيانات الإعفاءات الضريبية
     */
    protected function getTaxExemptionData($periodStart, $periodEnd)
    {
        return [
            'summary' => [
                'total_exemptions' => 0,
                'total_savings' => 0,
            ],
            'exemptions' => [],
            'savings' => 0,
        ];
    }

    /**
     * حساب بيانات الإقرار الضريبي
     */
    protected function calculateTaxReturnData($returnType, $periodStart, $periodEnd)
    {
        $totalSales = $this->getTaxableSales($periodStart, $periodEnd);
        $exemptSales = $this->getExemptSales($periodStart, $periodEnd);
        $outputTax = $this->getOutputTax($periodStart, $periodEnd);
        $inputTax = $this->getInputTax($periodStart, $periodEnd);
        $netTax = $outputTax - $inputTax;

        return [
            'total_sales' => $totalSales + $exemptSales,
            'taxable_sales' => $totalSales,
            'exempt_sales' => $exemptSales,
            'output_tax' => $outputTax,
            'input_tax' => $inputTax,
            'net_tax' => $netTax,
            'total_due' => max($netTax, 0), // لا يمكن أن يكون سالباً
            'breakdown' => [],
        ];
    }

    /**
     * توليد رقم الإقرار
     */
    protected function generateReturnNumber($returnType, $periodStart)
    {
        $prefix = strtoupper(substr($returnType, 0, 3));
        $year = $periodStart->format('Y');
        $month = $periodStart->format('m');
        
        $lastReturn = TaxReturn::where('return_number', 'like', "{$prefix}-{$year}-{$month}-%")
                              ->orderBy('return_number', 'desc')
                              ->first();

        if ($lastReturn) {
            $lastNumber = (int) substr($lastReturn->return_number, -3);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return "{$prefix}-{$year}-{$month}-" . str_pad($newNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * حساب تاريخ الاستحقاق
     */
    protected function calculateDueDate($periodType, $periodEnd)
    {
        switch ($periodType) {
            case 'monthly':
                return $periodEnd->copy()->addDays(20); // 20 يوم من نهاية الشهر
            case 'quarterly':
                return $periodEnd->copy()->addDays(30); // 30 يوم من نهاية الربع
            case 'annual':
                return $periodEnd->copy()->addDays(90); // 90 يوم من نهاية السنة
            default:
                return $periodEnd->copy()->addDays(20);
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class CustomerController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request)
    {
        $query = Customer::query();

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->has('customer_type')) {
            $query->where('customer_type', $request->customer_type);
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // فلترة العملاء المتجاوزين للحد الائتماني
        if ($request->boolean('exceeded_credit_limit')) {
            $query->whereRaw('current_balance > credit_limit');
        }

        $customers = $query->orderBy('name')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * عرض تفاصيل عميل محدد
     */
    public function show($id)
    {
        $customer = Customer::with(['salesInvoices', 'payments'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => $customer,
                'has_exceeded_credit_limit' => $customer->hasExceededCreditLimit(),
                'available_credit' => $customer->getAvailableCredit(),
                'aging_analysis' => $customer->getAgingAnalysis(),
                'overdue_invoices_count' => $customer->getOverdueInvoices()->count(),
            ],
        ]);
    }

    /**
     * إنشاء عميل جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'tax_number' => 'nullable|string|max:50',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'customer_type' => 'required|in:individual,company',
            'opening_balance' => 'nullable|numeric',
            'notes' => 'nullable|string',
        ]);

        $customer = Customer::create([
            'code' => Customer::generateCustomerCode(),
            'name' => $request->name,
            'name_en' => $request->name_en,
            'phone' => $request->phone,
            'email' => $request->email,
            'address' => $request->address,
            'tax_number' => $request->tax_number,
            'credit_limit' => $request->credit_limit ?? 0,
            'payment_terms' => $request->payment_terms ?? 30,
            'customer_type' => $request->customer_type,
            'opening_balance' => $request->opening_balance ?? 0,
            'current_balance' => $request->opening_balance ?? 0,
            'notes' => $request->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء العميل بنجاح',
            'data' => $customer,
        ], 201);
    }

    /**
     * تحديث عميل
     */
    public function update(Request $request, $id)
    {
        $customer = Customer::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string',
            'tax_number' => 'nullable|string|max:50',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|integer|min:0|max:365',
            'customer_type' => 'required|in:individual,company',
            'notes' => 'nullable|string',
        ]);

        $customer->update($request->only([
            'name', 'name_en', 'phone', 'email', 'address', 'tax_number',
            'credit_limit', 'payment_terms', 'customer_type', 'notes'
        ]));

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث العميل بنجاح',
            'data' => $customer,
        ]);
    }

    /**
     * حذف عميل
     */
    public function destroy($id)
    {
        $customer = Customer::findOrFail($id);

        // التحقق من وجود فواتير
        if ($customer->salesInvoices()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف العميل لأنه يحتوي على فواتير',
            ], 422);
        }

        $customer->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف العميل بنجاح',
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل عميل
     */
    public function toggleStatus($id)
    {
        $customer = Customer::findOrFail($id);
        $customer->update(['is_active' => !$customer->is_active]);

        $status = $customer->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return response()->json([
            'success' => true,
            'message' => "{$status} العميل بنجاح",
            'data' => $customer,
        ]);
    }

    /**
     * كشف حساب العميل
     */
    public function statement(Request $request, $id)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
        ]);

        $customer = Customer::findOrFail($id);
        $statement = $customer->getStatement($request->from_date, $request->to_date);

        return response()->json([
            'success' => true,
            'data' => $statement,
        ]);
    }

    /**
     * تحليل الأعمار للعميل
     */
    public function agingAnalysis($id)
    {
        $customer = Customer::findOrFail($id);
        $aging = $customer->getAgingAnalysis();

        return response()->json([
            'success' => true,
            'data' => [
                'customer' => $customer,
                'aging' => $aging,
                'total_outstanding' => array_sum($aging),
            ],
        ]);
    }

    /**
     * الحصول على العملاء النشطين فقط
     */
    public function getActiveCustomers()
    {
        $customers = Customer::active()
                           ->orderBy('name')
                           ->get(['id', 'code', 'name', 'phone', 'credit_limit', 'current_balance']);

        return response()->json([
            'success' => true,
            'data' => $customers,
        ]);
    }

    /**
     * إحصائيات العملاء
     */
    public function getStatistics()
    {
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::active()->count(),
            'individual_customers' => Customer::ofType('individual')->count(),
            'company_customers' => Customer::ofType('company')->count(),
            'customers_exceeded_credit' => Customer::whereRaw('current_balance > credit_limit')->count(),
            'total_receivables' => Customer::sum('current_balance'),
            'average_balance' => Customer::avg('current_balance'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * تقرير العملاء المتجاوزين للحد الائتماني
     */
    public function exceededCreditLimitReport()
    {
        $customers = Customer::whereRaw('current_balance > credit_limit')
                           ->where('is_active', true)
                           ->orderByDesc('current_balance')
                           ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'customers' => $customers,
                'total_exceeded_amount' => $customers->sum(function ($customer) {
                    return $customer->current_balance - $customer->credit_limit;
                }),
            ],
        ]);
    }
}

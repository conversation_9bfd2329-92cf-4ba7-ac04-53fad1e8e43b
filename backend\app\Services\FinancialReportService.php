<?php

namespace App\Services;

use App\Models\Account;
use App\Models\JournalEntry;
use App\Models\JournalEntryDetail;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class FinancialReportService
{
    /**
     * إنشاء قائمة الدخل
     */
    public function generateIncomeStatement($fromDate, $toDate, $branchId = null)
    {
        $fromDate = Carbon::parse($fromDate);
        $toDate = Carbon::parse($toDate);

        // الإيرادات
        $revenues = $this->getAccountBalances('revenue', $fromDate, $toDate, $branchId);
        $totalRevenues = $revenues->sum('balance');

        // المصروفات
        $expenses = $this->getAccountBalances('expense', $fromDate, $toDate, $branchId);
        $totalExpenses = $expenses->sum('balance');

        // صافي الربح/الخسارة
        $netIncome = $totalRevenues - $totalExpenses;

        return [
            'period' => [
                'from' => $fromDate->format('Y-m-d'),
                'to' => $toDate->format('Y-m-d'),
            ],
            'revenues' => [
                'accounts' => $revenues,
                'total' => $totalRevenues,
            ],
            'expenses' => [
                'accounts' => $expenses,
                'total' => $totalExpenses,
            ],
            'net_income' => $netIncome,
            'profit_margin' => $totalRevenues > 0 ? ($netIncome / $totalRevenues) * 100 : 0,
        ];
    }

    /**
     * إنشاء الميزانية العمومية
     */
    public function generateBalanceSheet($asOfDate, $branchId = null)
    {
        $asOfDate = Carbon::parse($asOfDate);

        // الأصول
        $assets = $this->getAccountBalances('asset', null, $asOfDate, $branchId);
        $totalAssets = $assets->sum('balance');

        // الخصوم
        $liabilities = $this->getAccountBalances('liability', null, $asOfDate, $branchId);
        $totalLiabilities = $liabilities->sum('balance');

        // حقوق الملكية
        $equity = $this->getAccountBalances('equity', null, $asOfDate, $branchId);
        $totalEquity = $equity->sum('balance');

        // إضافة صافي الدخل لحقوق الملكية
        $currentYearIncome = $this->generateIncomeStatement(
            $asOfDate->startOfYear()->format('Y-m-d'),
            $asOfDate->format('Y-m-d'),
            $branchId
        )['net_income'];

        $totalEquity += $currentYearIncome;

        return [
            'as_of_date' => $asOfDate->format('Y-m-d'),
            'assets' => [
                'accounts' => $assets,
                'total' => $totalAssets,
            ],
            'liabilities' => [
                'accounts' => $liabilities,
                'total' => $totalLiabilities,
            ],
            'equity' => [
                'accounts' => $equity,
                'current_year_income' => $currentYearIncome,
                'total' => $totalEquity,
            ],
            'total_liabilities_and_equity' => $totalLiabilities + $totalEquity,
            'is_balanced' => abs($totalAssets - ($totalLiabilities + $totalEquity)) < 0.01,
        ];
    }

    /**
     * إنشاء قائمة التدفقات النقدية
     */
    public function generateCashFlowStatement($fromDate, $toDate, $branchId = null)
    {
        $fromDate = Carbon::parse($fromDate);
        $toDate = Carbon::parse($toDate);

        // الحسابات النقدية
        $cashAccounts = Account::where('account_type', 'asset')
                              ->where(function ($query) {
                                  $query->where('name', 'like', '%نقدية%')
                                        ->orWhere('name', 'like', '%صندوق%')
                                        ->orWhere('name', 'like', '%بنك%')
                                        ->orWhere('name_en', 'like', '%cash%')
                                        ->orWhere('name_en', 'like', '%bank%');
                              })
                              ->get();

        $cashFlows = [];
        $totalCashFlow = 0;

        foreach ($cashAccounts as $account) {
            $openingBalance = $account->getBalanceAsOf($fromDate->subDay()->format('Y-m-d'));
            $closingBalance = $account->getBalanceAsOf($toDate->format('Y-m-d'));
            $netChange = $closingBalance - $openingBalance;

            $cashFlows[] = [
                'account' => $account,
                'opening_balance' => $openingBalance,
                'closing_balance' => $closingBalance,
                'net_change' => $netChange,
            ];

            $totalCashFlow += $netChange;
        }

        // تحليل التدفقات حسب النشاط
        $operatingCashFlow = $this->getOperatingCashFlow($fromDate, $toDate, $branchId);
        $investingCashFlow = $this->getInvestingCashFlow($fromDate, $toDate, $branchId);
        $financingCashFlow = $this->getFinancingCashFlow($fromDate, $toDate, $branchId);

        return [
            'period' => [
                'from' => $fromDate->format('Y-m-d'),
                'to' => $toDate->format('Y-m-d'),
            ],
            'cash_accounts' => $cashFlows,
            'operating_cash_flow' => $operatingCashFlow,
            'investing_cash_flow' => $investingCashFlow,
            'financing_cash_flow' => $financingCashFlow,
            'net_cash_flow' => $totalCashFlow,
        ];
    }

    /**
     * إنشاء تقرير الأرباح والخسائر التفصيلي
     */
    public function generateDetailedProfitLoss($fromDate, $toDate, $branchId = null)
    {
        $incomeStatement = $this->generateIncomeStatement($fromDate, $toDate, $branchId);

        // تفصيل أكثر للإيرادات والمصروفات
        $revenueBreakdown = $this->getAccountBreakdown('revenue', $fromDate, $toDate, $branchId);
        $expenseBreakdown = $this->getAccountBreakdown('expense', $fromDate, $toDate, $branchId);

        // حساب النسب المالية
        $ratios = $this->calculateProfitabilityRatios($incomeStatement);

        return [
            'basic_statement' => $incomeStatement,
            'revenue_breakdown' => $revenueBreakdown,
            'expense_breakdown' => $expenseBreakdown,
            'ratios' => $ratios,
        ];
    }

    /**
     * الحصول على أرصدة الحسابات
     */
    protected function getAccountBalances($accountType, $fromDate = null, $toDate = null, $branchId = null)
    {
        $accounts = Account::where('account_type', $accountType)
                          ->where('is_active', true)
                          ->get();

        $balances = collect();

        foreach ($accounts as $account) {
            $balance = 0;

            if ($fromDate && $toDate) {
                // حساب الرصيد خلال فترة (للإيرادات والمصروفات)
                $movements = $account->journalEntryDetails()
                                   ->whereHas('journalEntry', function ($query) use ($fromDate, $toDate, $branchId) {
                                       $query->whereBetween('entry_date', [$fromDate, $toDate])
                                             ->where('status', 'approved');
                                       if ($branchId) {
                                           $query->where('branch_id', $branchId);
                                       }
                                   })
                                   ->get();

                foreach ($movements as $movement) {
                    if ($accountType === 'revenue') {
                        $balance += $movement->credit - $movement->debit;
                    } else {
                        $balance += $movement->debit - $movement->credit;
                    }
                }
            } else {
                // حساب الرصيد التراكمي (للأصول والخصوم وحقوق الملكية)
                $balance = $account->getBalanceAsOf($toDate->format('Y-m-d'));
            }

            if ($balance != 0) {
                $balances->push([
                    'account' => $account,
                    'balance' => $balance,
                ]);
            }
        }

        return $balances;
    }

    /**
     * الحصول على تفصيل الحسابات
     */
    protected function getAccountBreakdown($accountType, $fromDate, $toDate, $branchId = null)
    {
        $parentAccounts = Account::where('account_type', $accountType)
                                ->whereNull('parent_id')
                                ->where('is_active', true)
                                ->get();

        $breakdown = [];

        foreach ($parentAccounts as $parent) {
            $children = $this->getAccountBalances($accountType, $fromDate, $toDate, $branchId)
                            ->filter(function ($item) use ($parent) {
                                return $item['account']->parent_id === $parent->id ||
                                       $item['account']->id === $parent->id;
                            });

            if ($children->isNotEmpty()) {
                $breakdown[] = [
                    'parent_account' => $parent,
                    'children' => $children,
                    'total' => $children->sum('balance'),
                ];
            }
        }

        return $breakdown;
    }

    /**
     * حساب التدفقات النقدية التشغيلية
     */
    protected function getOperatingCashFlow($fromDate, $toDate, $branchId = null)
    {
        // هذا مبسط - يحتاج تطوير أكثر
        return 0;
    }

    /**
     * حساب التدفقات النقدية الاستثمارية
     */
    protected function getInvestingCashFlow($fromDate, $toDate, $branchId = null)
    {
        // هذا مبسط - يحتاج تطوير أكثر
        return 0;
    }

    /**
     * حساب التدفقات النقدية التمويلية
     */
    protected function getFinancingCashFlow($fromDate, $toDate, $branchId = null)
    {
        // هذا مبسط - يحتاج تطوير أكثر
        return 0;
    }

    /**
     * حساب النسب المالية للربحية
     */
    protected function calculateProfitabilityRatios($incomeStatement)
    {
        $totalRevenues = $incomeStatement['revenues']['total'];
        $totalExpenses = $incomeStatement['expenses']['total'];
        $netIncome = $incomeStatement['net_income'];

        return [
            'gross_profit_margin' => $totalRevenues > 0 ? ($netIncome / $totalRevenues) * 100 : 0,
            'expense_ratio' => $totalRevenues > 0 ? ($totalExpenses / $totalRevenues) * 100 : 0,
            'revenue_growth' => 0, // يحتاج مقارنة مع فترة سابقة
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CostCenter extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'description',
        'parent_id',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the parent cost center.
     */
    public function parent()
    {
        return $this->belongsTo(CostCenter::class, 'parent_id');
    }

    /**
     * Get the child cost centers.
     */
    public function children()
    {
        return $this->hasMany(CostCenter::class, 'parent_id');
    }

    /**
     * Get all descendants (children, grandchildren, etc.)
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get the journal entry details for this cost center.
     */
    public function journalEntryDetails()
    {
        return $this->hasMany(JournalEntryDetail::class);
    }

    /**
     * Scope a query to only include active cost centers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include parent cost centers.
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Check if cost center can be deleted.
     */
    public function canBeDeleted()
    {
        return $this->children()->count() === 0 && 
               $this->journalEntryDetails()->count() === 0;
    }
}

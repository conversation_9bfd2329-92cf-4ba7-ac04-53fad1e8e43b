<?php

namespace App\Http\Controllers;

use App\Models\TaxType;
use App\Services\TaxCalculationService;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class TaxTypeController extends Controller
{
    protected $taxCalculationService;

    public function __construct(TaxCalculationService $taxCalculationService)
    {
        $this->taxCalculationService = $taxCalculationService;
    }

    /**
     * عرض قائمة أنواع الضرائب
     */
    public function index(Request $request)
    {
        $query = TaxType::query();

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        $taxTypes = $query->orderBy('name')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $taxTypes,
        ]);
    }

    /**
     * عرض تفاصيل نوع ضريبة محدد
     */
    public function show($id)
    {
        $taxType = TaxType::findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $taxType,
        ]);
    }

    /**
     * إنشاء نوع ضريبة جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:tax_types',
            'rate' => 'required|numeric|min:0',
            'type' => 'required|in:percentage,fixed',
            'calculation_method' => 'required|in:inclusive,exclusive',
            'description' => 'nullable|string',
        ]);

        $taxType = TaxType::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء نوع الضريبة بنجاح',
            'data' => $taxType,
        ], 201);
    }

    /**
     * تحديث نوع ضريبة
     */
    public function update(Request $request, $id)
    {
        $taxType = TaxType::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('tax_types')->ignore($id)],
            'rate' => 'required|numeric|min:0',
            'type' => 'required|in:percentage,fixed',
            'calculation_method' => 'required|in:inclusive,exclusive',
            'description' => 'nullable|string',
        ]);

        $taxType->update($request->all());

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث نوع الضريبة بنجاح',
            'data' => $taxType,
        ]);
    }

    /**
     * حذف نوع ضريبة
     */
    public function destroy($id)
    {
        $taxType = TaxType::findOrFail($id);

        // التحقق من عدم استخدام نوع الضريبة
        if ($taxType->items()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف نوع الضريبة لأنه مستخدم في أصناف',
            ], 422);
        }

        $taxType->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف نوع الضريبة بنجاح',
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل نوع ضريبة
     */
    public function toggleStatus($id)
    {
        $taxType = TaxType::findOrFail($id);
        $taxType->update(['is_active' => !$taxType->is_active]);

        $status = $taxType->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return response()->json([
            'success' => true,
            'message' => "{$status} نوع الضريبة بنجاح",
            'data' => $taxType,
        ]);
    }

    /**
     * حساب الضريبة لمبلغ معين
     */
    public function calculateTax(Request $request)
    {
        $request->validate([
            'tax_type_id' => 'required|exists:tax_types,id',
            'amount' => 'required|numeric|min:0',
            'custom_rate' => 'nullable|numeric|min:0',
        ]);

        $taxType = TaxType::findOrFail($request->tax_type_id);
        $amount = $request->amount;
        $customRate = $request->custom_rate;

        $taxAmount = $taxType->calculateTax($amount, $customRate);
        $totalAmount = $taxType->calculateTotalAmount($amount, $customRate);
        $baseAmount = $taxType->calculateBaseAmount($totalAmount, $customRate);

        return response()->json([
            'success' => true,
            'data' => [
                'tax_type' => $taxType,
                'base_amount' => round($amount, 2),
                'tax_amount' => round($taxAmount, 2),
                'total_amount' => round($totalAmount, 2),
                'effective_rate' => $customRate ?? $taxType->rate,
                'calculation_method' => $taxType->calculation_method,
            ],
        ]);
    }

    /**
     * حساب الضريبة العكسية (من المبلغ الشامل للضريبة)
     */
    public function calculateReverseTax(Request $request)
    {
        $request->validate([
            'inclusive_amount' => 'required|numeric|min:0',
            'tax_rate' => 'required|numeric|min:0',
        ]);

        $calculation = $this->taxCalculationService->calculateReverseTax(
            $request->inclusive_amount,
            $request->tax_rate
        );

        return response()->json([
            'success' => true,
            'data' => $calculation,
        ]);
    }

    /**
     * الحصول على أنواع الضرائب النشطة فقط
     */
    public function getActiveTaxTypes()
    {
        $taxTypes = TaxType::active()->orderBy('name')->get();

        return response()->json([
            'success' => true,
            'data' => $taxTypes,
        ]);
    }

    /**
     * الحصول على إحصائيات أنواع الضرائب
     */
    public function getStatistics()
    {
        $stats = [
            'total_tax_types' => TaxType::count(),
            'active_tax_types' => TaxType::active()->count(),
            'percentage_based' => TaxType::percentage()->count(),
            'fixed_amount' => TaxType::fixed()->count(),
            'most_used' => TaxType::withCount('items')
                                 ->orderBy('items_count', 'desc')
                                 ->limit(5)
                                 ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}

<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="AccountingSystem.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="AuthToken" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="UserId" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="UserName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="UserEmail" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RememberLogin" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar</Value>
    </Setting>
    <Setting Name="Theme" Type="System.String" Scope="User">
      <Value Profile="(Default)">Light</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.String" Scope="User">
      <Value Profile="(Default)">Maximized</Value>
    </Setting>
    <Setting Name="WindowSize" Type="System.Drawing.Size" Scope="User">
      <Value Profile="(Default)">1200, 800</Value>
    </Setting>
    <Setting Name="WindowLocation" Type="System.Drawing.Point" Scope="User">
      <Value Profile="(Default)">0, 0</Value>
    </Setting>
  </Settings>
</SettingsFile>

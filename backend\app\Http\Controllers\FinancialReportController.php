<?php

namespace App\Http\Controllers;

use App\Services\FinancialReportService;
use Illuminate\Http\Request;

class FinancialReportController extends Controller
{
    protected $financialReportService;

    public function __construct(FinancialReportService $financialReportService)
    {
        $this->financialReportService = $financialReportService;
    }

    /**
     * قائمة الدخل
     */
    public function incomeStatement(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $report = $this->financialReportService->generateIncomeStatement(
            $request->from_date,
            $request->to_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * الميزانية العمومية
     */
    public function balanceSheet(Request $request)
    {
        $request->validate([
            'as_of_date' => 'required|date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $report = $this->financialReportService->generateBalanceSheet(
            $request->as_of_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * قائمة التدفقات النقدية
     */
    public function cashFlowStatement(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $report = $this->financialReportService->generateCashFlowStatement(
            $request->from_date,
            $request->to_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * تقرير الأرباح والخسائر التفصيلي
     */
    public function detailedProfitLoss(Request $request)
    {
        $request->validate([
            'from_date' => 'required|date',
            'to_date' => 'required|date|after_or_equal:from_date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $report = $this->financialReportService->generateDetailedProfitLoss(
            $request->from_date,
            $request->to_date,
            $request->branch_id
        );

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * تقرير مقارن للفترات
     */
    public function comparativeReport(Request $request)
    {
        $request->validate([
            'current_from' => 'required|date',
            'current_to' => 'required|date|after_or_equal:current_from',
            'previous_from' => 'required|date',
            'previous_to' => 'required|date|after_or_equal:previous_from',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $currentPeriod = $this->financialReportService->generateIncomeStatement(
            $request->current_from,
            $request->current_to,
            $request->branch_id
        );

        $previousPeriod = $this->financialReportService->generateIncomeStatement(
            $request->previous_from,
            $request->previous_to,
            $request->branch_id
        );

        // حساب التغييرات والنسب
        $comparison = $this->calculateComparison($currentPeriod, $previousPeriod);

        return response()->json([
            'success' => true,
            'data' => [
                'current_period' => $currentPeriod,
                'previous_period' => $previousPeriod,
                'comparison' => $comparison,
            ],
        ]);
    }

    /**
     * تقرير الحسابات الفرعية
     */
    public function accountsReport(Request $request)
    {
        $request->validate([
            'account_type' => 'nullable|in:asset,liability,equity,revenue,expense',
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
            'branch_id' => 'nullable|exists:branches,id',
            'parent_account_id' => 'nullable|exists:accounts,id',
        ]);

        // هذا يحتاج تطوير أكثر
        return response()->json([
            'success' => true,
            'message' => 'تقرير الحسابات الفرعية قيد التطوير',
        ]);
    }

    /**
     * تقرير النسب المالية
     */
    public function financialRatios(Request $request)
    {
        $request->validate([
            'as_of_date' => 'required|date',
            'branch_id' => 'nullable|exists:branches,id',
        ]);

        $balanceSheet = $this->financialReportService->generateBalanceSheet(
            $request->as_of_date,
            $request->branch_id
        );

        $incomeStatement = $this->financialReportService->generateIncomeStatement(
            now()->startOfYear()->format('Y-m-d'),
            $request->as_of_date,
            $request->branch_id
        );

        $ratios = $this->calculateFinancialRatios($balanceSheet, $incomeStatement);

        return response()->json([
            'success' => true,
            'data' => [
                'as_of_date' => $request->as_of_date,
                'ratios' => $ratios,
            ],
        ]);
    }

    /**
     * حساب المقارنة بين الفترات
     */
    protected function calculateComparison($current, $previous)
    {
        $revenueChange = $current['revenues']['total'] - $previous['revenues']['total'];
        $revenueChangePercent = $previous['revenues']['total'] > 0 
            ? ($revenueChange / $previous['revenues']['total']) * 100 
            : 0;

        $expenseChange = $current['expenses']['total'] - $previous['expenses']['total'];
        $expenseChangePercent = $previous['expenses']['total'] > 0 
            ? ($expenseChange / $previous['expenses']['total']) * 100 
            : 0;

        $netIncomeChange = $current['net_income'] - $previous['net_income'];
        $netIncomeChangePercent = $previous['net_income'] != 0 
            ? ($netIncomeChange / abs($previous['net_income'])) * 100 
            : 0;

        return [
            'revenue_change' => [
                'amount' => $revenueChange,
                'percentage' => $revenueChangePercent,
            ],
            'expense_change' => [
                'amount' => $expenseChange,
                'percentage' => $expenseChangePercent,
            ],
            'net_income_change' => [
                'amount' => $netIncomeChange,
                'percentage' => $netIncomeChangePercent,
            ],
        ];
    }

    /**
     * حساب النسب المالية
     */
    protected function calculateFinancialRatios($balanceSheet, $incomeStatement)
    {
        $totalAssets = $balanceSheet['assets']['total'];
        $totalLiabilities = $balanceSheet['liabilities']['total'];
        $totalEquity = $balanceSheet['equity']['total'];
        $totalRevenues = $incomeStatement['revenues']['total'];
        $netIncome = $incomeStatement['net_income'];

        return [
            'liquidity_ratios' => [
                'debt_to_equity' => $totalEquity > 0 ? $totalLiabilities / $totalEquity : 0,
                'debt_to_assets' => $totalAssets > 0 ? $totalLiabilities / $totalAssets : 0,
            ],
            'profitability_ratios' => [
                'return_on_assets' => $totalAssets > 0 ? ($netIncome / $totalAssets) * 100 : 0,
                'return_on_equity' => $totalEquity > 0 ? ($netIncome / $totalEquity) * 100 : 0,
                'profit_margin' => $totalRevenues > 0 ? ($netIncome / $totalRevenues) * 100 : 0,
            ],
            'efficiency_ratios' => [
                'asset_turnover' => $totalAssets > 0 ? $totalRevenues / $totalAssets : 0,
            ],
        ];
    }
}

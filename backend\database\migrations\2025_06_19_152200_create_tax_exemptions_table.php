<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_exemptions', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الإعفاء
            $table->string('code')->unique(); // رمز الإعفاء
            $table->enum('exemption_type', ['full', 'partial', 'conditional']); // نوع الإعفاء
            $table->decimal('exemption_percentage', 5, 2)->nullable(); // نسبة الإعفاء للجزئي
            $table->decimal('exemption_amount', 15, 2)->nullable(); // مبلغ الإعفاء الثابت
            $table->text('description')->nullable();
            $table->json('conditions')->nullable(); // شروط الإعفاء
            $table->json('applicable_tax_types')->nullable(); // أنواع الضرائب المطبقة
            $table->date('valid_from')->nullable();
            $table->date('valid_to')->nullable();
            $table->boolean('is_active')->default(true);
            $table->text('legal_reference')->nullable(); // المرجع القانوني
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_exemptions');
    }
};

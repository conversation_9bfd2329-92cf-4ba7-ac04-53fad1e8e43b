using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Sales
{
    public partial class POSForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;

        // Customer section
        private ComboBox? _customerComboBox;
        private Button? _addCustomerButton;
        private Label? _customerBalanceLabel;

        // Items section
        private TextBox? _barcodeTextBox;
        private ComboBox? _itemComboBox;
        private NumericUpDown? _quantityNumeric;
        private Button? _addItemButton;

        // Invoice items grid
        private DataGridView? _invoiceItemsGrid;

        // Totals section
        private Label? _subtotalLabel;
        private Label? _taxLabel;
        private Label? _discountLabel;
        private Label? _totalLabel;
        private NumericUpDown? _discountNumeric;

        // Payment section
        private ComboBox? _paymentMethodComboBox;
        private NumericUpDown? _paidAmountNumeric;
        private Label? _changeLabel;

        // Action buttons
        private Button? _holdButton;
        private Button? _clearButton;
        private Button? _payButton;
        private Button? _printButton;

        // Data
        private List<Customer> _customers = new();
        private List<Item> _items = new();
        private List<InvoiceItem> _invoiceItems = new();
        private decimal _subtotal = 0;
        private decimal _taxAmount = 0;
        private decimal _discountAmount = 0;
        private decimal _total = 0;

        public POSForm(IApiService apiService, IAuthService authService)
        {
            _apiService = apiService;
            _authService = authService;
            InitializeComponent();
            SetupForm();
            LoadInitialDataAsync();
        }

        private void SetupForm()
        {
            this.Text = "نقطة البيع";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterParent;
            this.WindowState = FormWindowState.Maximized;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.BackColor = Color.FromArgb(248, 249, 250);

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Header panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(0, 122, 204)
            };

            var titleLabel = new Label
            {
                Text = "نقطة البيع",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 25),
                Size = new Size(200, 30)
            };

            var dateTimeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.White,
                Location = new Point(1200, 30),
                Size = new Size(150, 20)
            };

            headerPanel.Controls.AddRange(new Control[] { titleLabel, dateTimeLabel });

            // Main container
            var mainContainer = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 3,
                RowCount = 1,
                BackColor = Color.Transparent
            };

            mainContainer.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F)); // Left panel
            mainContainer.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 40F)); // Center panel
            mainContainer.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 20F)); // Right panel

            // Left panel - Customer and Items
            var leftPanel = CreateLeftPanel();

            // Center panel - Invoice Items
            var centerPanel = CreateCenterPanel();

            // Right panel - Totals and Payment
            var rightPanel = CreateRightPanel();

            mainContainer.Controls.Add(leftPanel, 0, 0);
            mainContainer.Controls.Add(centerPanel, 1, 0);
            mainContainer.Controls.Add(rightPanel, 2, 0);

            this.Controls.AddRange(new Control[] { mainContainer, headerPanel });
        }

        private Panel CreateLeftPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(10)
            };

            // Customer section
            var customerGroupBox = new GroupBox
            {
                Text = "العميل",
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, 120),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _customerComboBox = new ComboBox
            {
                Location = new Point(10, 30),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };

            _addCustomerButton = new Button
            {
                Text = "إضافة عميل",
                Location = new Point(320, 30),
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _customerBalanceLabel = new Label
            {
                Text = "الرصيد: 0.00",
                Location = new Point(10, 65),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10)
            };

            customerGroupBox.Controls.AddRange(new Control[]
            {
                _customerComboBox, _addCustomerButton, _customerBalanceLabel
            });

            // Items section
            var itemsGroupBox = new GroupBox
            {
                Text = "إضافة صنف",
                Location = new Point(10, 140),
                Size = new Size(panel.Width - 20, 150),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            var barcodeLabel = new Label
            {
                Text = "الباركود:",
                Location = new Point(10, 30),
                Size = new Size(80, 20)
            };

            _barcodeTextBox = new TextBox
            {
                Location = new Point(100, 30),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            var itemLabel = new Label
            {
                Text = "الصنف:",
                Location = new Point(10, 65),
                Size = new Size(80, 20)
            };

            _itemComboBox = new ComboBox
            {
                Location = new Point(100, 65),
                Size = new Size(300, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 10)
            };

            var quantityLabel = new Label
            {
                Text = "الكمية:",
                Location = new Point(10, 100),
                Size = new Size(80, 20)
            };

            _quantityNumeric = new NumericUpDown
            {
                Location = new Point(100, 100),
                Size = new Size(100, 25),
                Minimum = 1,
                Maximum = 9999,
                Value = 1,
                DecimalPlaces = 2
            };

            _addItemButton = new Button
            {
                Text = "إضافة",
                Location = new Point(220, 100),
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            itemsGroupBox.Controls.AddRange(new Control[]
            {
                barcodeLabel, _barcodeTextBox, itemLabel, _itemComboBox,
                quantityLabel, _quantityNumeric, _addItemButton
            });

            panel.Controls.AddRange(new Control[] { customerGroupBox, itemsGroupBox });
            return panel;
        }

        private Panel CreateCenterPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(5, 10, 5, 10)
            };

            var invoiceGroupBox = new GroupBox
            {
                Text = "أصناف الفاتورة",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _invoiceItemsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                Margin = new Padding(10)
            };

            SetupInvoiceItemsGrid();

            invoiceGroupBox.Controls.Add(_invoiceItemsGrid);
            panel.Controls.Add(invoiceGroupBox);
            return panel;
        }

        private Panel CreateRightPanel()
        {
            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Margin = new Padding(10)
            };

            // Totals section
            var totalsGroupBox = new GroupBox
            {
                Text = "الإجماليات",
                Location = new Point(10, 10),
                Size = new Size(panel.Width - 20, 200),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _subtotalLabel = new Label
            {
                Text = "المجموع الفرعي: 0.00",
                Location = new Point(10, 30),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 11)
            };

            var discountLabel = new Label
            {
                Text = "الخصم:",
                Location = new Point(10, 65),
                Size = new Size(80, 20)
            };

            _discountNumeric = new NumericUpDown
            {
                Location = new Point(100, 65),
                Size = new Size(100, 25),
                Minimum = 0,
                Maximum = 100,
                DecimalPlaces = 2
            };

            _discountLabel = new Label
            {
                Text = "قيمة الخصم: 0.00",
                Location = new Point(10, 95),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10)
            };

            _taxLabel = new Label
            {
                Text = "الضريبة: 0.00",
                Location = new Point(10, 125),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 11)
            };

            _totalLabel = new Label
            {
                Text = "الإجمالي: 0.00",
                Location = new Point(10, 155),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(220, 53, 69)
            };

            totalsGroupBox.Controls.AddRange(new Control[]
            {
                _subtotalLabel, discountLabel, _discountNumeric, _discountLabel,
                _taxLabel, _totalLabel
            });

            // Payment section
            var paymentGroupBox = new GroupBox
            {
                Text = "الدفع",
                Location = new Point(10, 220),
                Size = new Size(panel.Width - 20, 150),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            var paymentMethodLabel = new Label
            {
                Text = "طريقة الدفع:",
                Location = new Point(10, 30),
                Size = new Size(100, 20)
            };

            _paymentMethodComboBox = new ComboBox
            {
                Location = new Point(10, 55),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _paymentMethodComboBox.Items.AddRange(new string[] { "نقدي", "بطاقة ائتمان", "تحويل بنكي", "آجل" });
            _paymentMethodComboBox.SelectedIndex = 0;

            var paidAmountLabel = new Label
            {
                Text = "المبلغ المدفوع:",
                Location = new Point(10, 85),
                Size = new Size(100, 20)
            };

            _paidAmountNumeric = new NumericUpDown
            {
                Location = new Point(10, 110),
                Size = new Size(150, 25),
                Minimum = 0,
                Maximum = 999999,
                DecimalPlaces = 2
            };

            _changeLabel = new Label
            {
                Text = "الباقي: 0.00",
                Location = new Point(170, 110),
                Size = new Size(100, 25),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(40, 167, 69)
            };

            paymentGroupBox.Controls.AddRange(new Control[]
            {
                paymentMethodLabel, _paymentMethodComboBox, paidAmountLabel,
                _paidAmountNumeric, _changeLabel
            });

            // Action buttons
            var buttonsPanel = new Panel
            {
                Location = new Point(10, 380),
                Size = new Size(panel.Width - 20, 200)
            };

            _holdButton = new Button
            {
                Text = "تعليق",
                Location = new Point(10, 10),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _clearButton = new Button
            {
                Text = "مسح",
                Location = new Point(10, 60),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _payButton = new Button
            {
                Text = "دفع",
                Location = new Point(10, 110),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _printButton = new Button
            {
                Text = "طباعة",
                Location = new Point(10, 160),
                Size = new Size(120, 40),
                BackColor = Color.FromArgb(13, 110, 253),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Enabled = false
            };

            buttonsPanel.Controls.AddRange(new Control[]
            {
                _holdButton, _clearButton, _payButton, _printButton
            });

            panel.Controls.AddRange(new Control[] { totalsGroupBox, paymentGroupBox, buttonsPanel });
            return panel;
        }

        private void SetupInvoiceItemsGrid()
        {
            if (_invoiceItemsGrid == null) return;

            _invoiceItemsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "ItemCode",
                    HeaderText = "كود الصنف",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "ItemName",
                    HeaderText = "اسم الصنف",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Quantity",
                    HeaderText = "الكمية",
                    Width = 80,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "UnitPrice",
                    HeaderText = "سعر الوحدة",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "TotalPrice",
                    HeaderText = "الإجمالي",
                    Width = 100,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewButtonColumn
                {
                    Name = "Delete",
                    HeaderText = "حذف",
                    Text = "حذف",
                    UseColumnTextForButtonValue = true,
                    Width = 60
                }
            });

            _invoiceItemsGrid.CellClick += InvoiceItemsGrid_CellClick;
        }

        private void SetupLayout()
        {
            // Additional layout setup if needed
        }

        private void SetupEvents()
        {
            if (_addCustomerButton != null)
                _addCustomerButton.Click += AddCustomerButton_Click;

            if (_barcodeTextBox != null)
                _barcodeTextBox.KeyPress += BarcodeTextBox_KeyPress;

            if (_itemComboBox != null)
                _itemComboBox.SelectedIndexChanged += ItemComboBox_SelectedIndexChanged;

            if (_addItemButton != null)
                _addItemButton.Click += AddItemButton_Click;

            if (_discountNumeric != null)
                _discountNumeric.ValueChanged += DiscountNumeric_ValueChanged;

            if (_paidAmountNumeric != null)
                _paidAmountNumeric.ValueChanged += PaidAmountNumeric_ValueChanged;

            if (_holdButton != null)
                _holdButton.Click += HoldButton_Click;

            if (_clearButton != null)
                _clearButton.Click += ClearButton_Click;

            if (_payButton != null)
                _payButton.Click += PayButton_Click;

            if (_printButton != null)
                _printButton.Click += PrintButton_Click;

            if (_customerComboBox != null)
                _customerComboBox.SelectedIndexChanged += CustomerComboBox_SelectedIndexChanged;
        }

        private async void LoadInitialDataAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;

                // Load customers
                var customersResponse = await _apiService.GetAsync<List<Customer>>("customers/active");
                if (customersResponse.Success && customersResponse.Data != null)
                {
                    _customers = customersResponse.Data;
                    PopulateCustomersComboBox();
                }

                // Load items
                var itemsResponse = await _apiService.GetAsync<List<Item>>("items/active");
                if (itemsResponse.Success && itemsResponse.Data != null)
                {
                    _items = itemsResponse.Data;
                    PopulateItemsComboBox();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PopulateCustomersComboBox()
        {
            if (_customerComboBox == null) return;

            _customerComboBox.Items.Clear();
            _customerComboBox.Items.Add(new { Text = "عميل نقدي", Value = 0 });

            foreach (var customer in _customers)
            {
                _customerComboBox.Items.Add(new { Text = customer.Name, Value = customer.Id });
            }

            _customerComboBox.DisplayMember = "Text";
            _customerComboBox.ValueMember = "Value";
            _customerComboBox.SelectedIndex = 0;
        }

        private void PopulateItemsComboBox()
        {
            if (_itemComboBox == null) return;

            _itemComboBox.Items.Clear();
            _itemComboBox.Items.Add(new { Text = "اختر صنف", Value = 0 });

            foreach (var item in _items)
            {
                _itemComboBox.Items.Add(new { Text = $"{item.Code} - {item.Name}", Value = item.Id });
            }

            _itemComboBox.DisplayMember = "Text";
            _itemComboBox.ValueMember = "Value";
            _itemComboBox.SelectedIndex = 0;
        }

        // Event handlers
        private void AddCustomerButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void BarcodeTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                SearchItemByBarcode();
            }
        }

        private void SearchItemByBarcode()
        {
            if (_barcodeTextBox == null || _itemComboBox == null) return;

            var barcode = _barcodeTextBox.Text.Trim();
            if (string.IsNullOrEmpty(barcode)) return;

            var item = _items.FirstOrDefault(i => i.Code == barcode);
            if (item != null)
            {
                // Select item in combo box
                for (int i = 0; i < _itemComboBox.Items.Count; i++)
                {
                    var comboItem = _itemComboBox.Items[i] as dynamic;
                    if (comboItem?.Value == item.Id)
                    {
                        _itemComboBox.SelectedIndex = i;
                        break;
                    }
                }

                // Add item to invoice
                AddItemButton_Click(null, EventArgs.Empty);
                _barcodeTextBox.Clear();
            }
            else
            {
                MessageBox.Show("لم يتم العثور على الصنف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _barcodeTextBox.SelectAll();
            }
        }

        private void ItemComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            // Update quantity focus when item is selected
            if (_itemComboBox?.SelectedIndex > 0)
            {
                _quantityNumeric?.Focus();
            }
        }

        private void AddItemButton_Click(object? sender, EventArgs e)
        {
            if (_itemComboBox?.SelectedIndex <= 0)
            {
                MessageBox.Show("يرجى اختيار صنف", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = _itemComboBox.SelectedItem as dynamic;
            var itemId = (int)selectedItem.Value;
            var item = _items.FirstOrDefault(i => i.Id == itemId);

            if (item == null) return;

            var quantity = (decimal)_quantityNumeric!.Value;

            // Check if item already exists in invoice
            var existingItem = _invoiceItems.FirstOrDefault(i => i.ItemId == itemId);
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
                existingItem.TotalPrice = existingItem.Quantity * existingItem.UnitPrice;
            }
            else
            {
                _invoiceItems.Add(new InvoiceItem
                {
                    ItemId = itemId,
                    ItemCode = item.Code,
                    ItemName = item.Name,
                    Quantity = quantity,
                    UnitPrice = item.SellingPrice,
                    TotalPrice = quantity * item.SellingPrice
                });
            }

            RefreshInvoiceItemsGrid();
            CalculateTotals();

            // Reset controls
            _itemComboBox.SelectedIndex = 0;
            _quantityNumeric.Value = 1;
            _barcodeTextBox?.Focus();
        }

        private void RefreshInvoiceItemsGrid()
        {
            if (_invoiceItemsGrid == null) return;

            _invoiceItemsGrid.Rows.Clear();

            foreach (var item in _invoiceItems)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_invoiceItemsGrid);

                row.Cells[0].Value = item.ItemCode;
                row.Cells[1].Value = item.ItemName;
                row.Cells[2].Value = item.Quantity;
                row.Cells[3].Value = item.UnitPrice;
                row.Cells[4].Value = item.TotalPrice;
                row.Cells[5].Value = "حذف";

                _invoiceItemsGrid.Rows.Add(row);
            }
        }

        private void CalculateTotals()
        {
            _subtotal = _invoiceItems.Sum(i => i.TotalPrice);
            _discountAmount = _subtotal * (_discountNumeric?.Value ?? 0) / 100;
            var afterDiscount = _subtotal - _discountAmount;
            _taxAmount = afterDiscount * 0.15m; // 15% VAT
            _total = afterDiscount + _taxAmount;

            UpdateTotalsDisplay();
        }

        private void UpdateTotalsDisplay()
        {
            if (_subtotalLabel != null) _subtotalLabel.Text = $"المجموع الفرعي: {_subtotal:N2}";
            if (_discountLabel != null) _discountLabel.Text = $"قيمة الخصم: {_discountAmount:N2}";
            if (_taxLabel != null) _taxLabel.Text = $"الضريبة: {_taxAmount:N2}";
            if (_totalLabel != null) _totalLabel.Text = $"الإجمالي: {_total:N2}";

            // Update paid amount to total if payment method is cash
            if (_paymentMethodComboBox?.SelectedIndex == 0 && _paidAmountNumeric != null)
            {
                _paidAmountNumeric.Value = _total;
            }

            CalculateChange();
        }

        private void CalculateChange()
        {
            if (_paidAmountNumeric == null || _changeLabel == null) return;

            var change = _paidAmountNumeric.Value - _total;
            _changeLabel.Text = $"الباقي: {Math.Max(0, change):N2}";
            _changeLabel.ForeColor = change >= 0 ? Color.FromArgb(40, 167, 69) : Color.FromArgb(220, 53, 69);
        }

        private void InvoiceItemsGrid_CellClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex == 5) // Delete button column
            {
                var result = MessageBox.Show("هل تريد حذف هذا الصنف؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    _invoiceItems.RemoveAt(e.RowIndex);
                    RefreshInvoiceItemsGrid();
                    CalculateTotals();
                }
            }
        }

        private void DiscountNumeric_ValueChanged(object? sender, EventArgs e)
        {
            CalculateTotals();
        }

        private void PaidAmountNumeric_ValueChanged(object? sender, EventArgs e)
        {
            CalculateChange();
        }

        private void CustomerComboBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (_customerComboBox?.SelectedItem != null)
            {
                var customerId = ((dynamic)_customerComboBox.SelectedItem).Value;
                var customer = _customers.FirstOrDefault(c => c.Id == customerId);

                if (customer != null && _customerBalanceLabel != null)
                {
                    _customerBalanceLabel.Text = $"الرصيد: {customer.CurrentBalance:N2}";
                    _customerBalanceLabel.ForeColor = customer.CurrentBalance > 0 ?
                        Color.FromArgb(220, 53, 69) : Color.FromArgb(40, 167, 69);
                }
                else if (_customerBalanceLabel != null)
                {
                    _customerBalanceLabel.Text = "الرصيد: 0.00";
                    _customerBalanceLabel.ForeColor = Color.Black;
                }
            }
        }

        private void HoldButton_Click(object? sender, EventArgs e)
        {
            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("لا توجد أصناف في الفاتورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            MessageBox.Show("تم تعليق الفاتورة", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
            ClearInvoice();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            if (_invoiceItems.Count > 0)
            {
                var result = MessageBox.Show("هل تريد مسح جميع الأصناف؟", "تأكيد المسح",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    ClearInvoice();
                }
            }
        }

        private async void PayButton_Click(object? sender, EventArgs e)
        {
            if (_invoiceItems.Count == 0)
            {
                MessageBox.Show("لا توجد أصناف في الفاتورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (_paidAmountNumeric?.Value < _total)
            {
                MessageBox.Show("المبلغ المدفوع أقل من إجمالي الفاتورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                this.Cursor = Cursors.WaitCursor;
                _payButton!.Enabled = false;
                _payButton.Text = "جاري المعالجة...";

                var customerId = ((dynamic)_customerComboBox!.SelectedItem).Value;
                var paymentMethod = _paymentMethodComboBox!.SelectedItem.ToString();

                var invoiceData = new
                {
                    customer_id = customerId > 0 ? customerId : (int?)null,
                    payment_method = paymentMethod,
                    subtotal = _subtotal,
                    discount_amount = _discountAmount,
                    tax_amount = _taxAmount,
                    total_amount = _total,
                    paid_amount = _paidAmountNumeric!.Value,
                    items = _invoiceItems.Select(i => new
                    {
                        item_id = i.ItemId,
                        quantity = i.Quantity,
                        unit_price = i.UnitPrice,
                        total_price = i.TotalPrice
                    }).ToArray()
                };

                var response = await _apiService.PostAsync<object>("sales-invoices", invoiceData);

                if (response.Success)
                {
                    MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    _printButton!.Enabled = true;
                    ClearInvoice();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
                _payButton!.Enabled = true;
                _payButton.Text = "دفع";
            }
        }

        private void PrintButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم طباعة الفاتورة", "قيد التطوير",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
            _printButton!.Enabled = false;
        }

        private void ClearInvoice()
        {
            _invoiceItems.Clear();
            RefreshInvoiceItemsGrid();
            CalculateTotals();

            if (_customerComboBox != null) _customerComboBox.SelectedIndex = 0;
            if (_paymentMethodComboBox != null) _paymentMethodComboBox.SelectedIndex = 0;
            if (_discountNumeric != null) _discountNumeric.Value = 0;
            if (_paidAmountNumeric != null) _paidAmountNumeric.Value = 0;
            if (_printButton != null) _printButton.Enabled = false;

            _barcodeTextBox?.Focus();
        }
    }
}
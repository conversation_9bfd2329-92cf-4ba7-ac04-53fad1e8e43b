<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_calculations', function (Blueprint $table) {
            $table->id();
            $table->string('calculable_type'); // نوع الكائن (فاتورة، بند، إلخ)
            $table->unsignedBigInteger('calculable_id'); // معرف الكائن
            $table->foreignId('tax_type_id')->constrained()->onDelete('restrict');
            $table->decimal('base_amount', 15, 4); // المبلغ الأساسي
            $table->decimal('taxable_amount', 15, 4); // المبلغ الخاضع للضريبة
            $table->decimal('tax_rate', 8, 4); // معدل الضريبة المطبق
            $table->decimal('tax_amount', 15, 4); // مبلغ الضريبة
            $table->decimal('exemption_amount', 15, 4)->default(0); // مبلغ الإعفاء
            $table->json('calculation_details'); // تفاصيل الحساب
            $table->json('applied_rules')->nullable(); // القواعد المطبقة
            $table->json('applied_exemptions')->nullable(); // الإعفاءات المطبقة
            $table->string('calculation_method'); // طريقة الحساب
            $table->timestamp('calculated_at');
            $table->foreignId('calculated_by')->constrained('users')->onDelete('restrict');
            $table->timestamps();

            $table->index(['calculable_type', 'calculable_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_calculations');
    }
};

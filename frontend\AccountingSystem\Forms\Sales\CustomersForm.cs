using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Sales
{
    public partial class CustomersForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;
        private DataGridView? _customersGrid;
        private TextBox? _searchTextBox;
        private ComboBox? _customerTypeComboBox;
        private CheckBox? _activeOnlyCheckBox;
        private CheckBox? _exceededCreditCheckBox;
        private Button? _addButton;
        private Button? _editButton;
        private Button? _deleteButton;
        private Button? _statementButton;
        private Button? _refreshButton;
        private Label? _totalCustomersLabel;
        private Label? _totalReceivablesLabel;
        private List<Customer> _customers = new();

        public CustomersForm(IApiService apiService, IAuthService authService)
        {
            _apiService = apiService;
            _authService = authService;
            InitializeComponent();
            SetupForm();
            LoadCustomersAsync();
        }

        private void SetupForm()
        {
            this.Text = "إدارة العملاء";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Filter panel
            var filterPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            // Search
            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(1050, 15),
                Size = new Size(50, 20)
            };

            _searchTextBox = new TextBox
            {
                Location = new Point(850, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            // Customer type filter
            var typeLabel = new Label
            {
                Text = "نوع العميل:",
                Location = new Point(750, 15),
                Size = new Size(80, 20)
            };

            _customerTypeComboBox = new ComboBox
            {
                Location = new Point(600, 12),
                Size = new Size(150, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            _customerTypeComboBox.Items.AddRange(new object[]
            {
                new { Text = "الكل", Value = "" },
                new { Text = "أفراد", Value = "individual" },
                new { Text = "شركات", Value = "company" }
            });
            _customerTypeComboBox.DisplayMember = "Text";
            _customerTypeComboBox.ValueMember = "Value";
            _customerTypeComboBox.SelectedIndex = 0;

            // Checkboxes
            _activeOnlyCheckBox = new CheckBox
            {
                Text = "النشطين فقط",
                Location = new Point(450, 15),
                Size = new Size(120, 20),
                Checked = true
            };

            _exceededCreditCheckBox = new CheckBox
            {
                Text = "متجاوزي الحد الائتماني",
                Location = new Point(250, 15),
                Size = new Size(180, 20)
            };

            var filterButton = new Button
            {
                Text = "فلترة",
                Location = new Point(150, 10),
                Size = new Size(70, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            filterPanel.Controls.AddRange(new Control[]
            {
                searchLabel, _searchTextBox, typeLabel, _customerTypeComboBox,
                _activeOnlyCheckBox, _exceededCreditCheckBox, filterButton
            });

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            _addButton = new Button
            {
                Text = "إضافة عميل",
                Location = new Point(10, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(120, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(210, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _statementButton = new Button
            {
                Text = "كشف حساب",
                Location = new Point(300, 10),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(13, 110, 253),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(410, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonsPanel.Controls.AddRange(new Control[]
            {
                _addButton, _editButton, _deleteButton, _statementButton, _refreshButton
            });

            // Statistics panel
            var statsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 40,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            _totalCustomersLabel = new Label
            {
                Text = "إجمالي العملاء: 0",
                Location = new Point(10, 10),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            _totalReceivablesLabel = new Label
            {
                Text = "إجمالي المستحقات: 0.00",
                Location = new Point(220, 10),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };

            statsPanel.Controls.AddRange(new Control[] { _totalCustomersLabel, _totalReceivablesLabel });

            // Customers grid
            _customersGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            SetupGridColumns();

            this.Controls.AddRange(new Control[] { _customersGrid, statsPanel, buttonsPanel, filterPanel });
        }

        private void SetupGridColumns()
        {
            if (_customersGrid == null) return;

            _customersGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    HeaderText = "كود العميل",
                    DataPropertyName = "Code",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Name",
                    HeaderText = "اسم العميل",
                    DataPropertyName = "Name",
                    Width = 250
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CustomerTypeDisplay",
                    HeaderText = "النوع",
                    DataPropertyName = "CustomerTypeDisplay",
                    Width = 80
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Phone",
                    HeaderText = "الهاتف",
                    DataPropertyName = "Phone",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Email",
                    HeaderText = "البريد الإلكتروني",
                    DataPropertyName = "Email",
                    Width = 200
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CreditLimit",
                    HeaderText = "الحد الائتماني",
                    DataPropertyName = "CreditLimit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CurrentBalance",
                    HeaderText = "الرصيد الحالي",
                    DataPropertyName = "CurrentBalance",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AvailableCredit",
                    HeaderText = "الائتمان المتاح",
                    DataPropertyName = "AvailableCredit",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "IsActive",
                    HeaderText = "نشط",
                    DataPropertyName = "IsActive",
                    Width = 60
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "HasExceededCreditLimit",
                    HeaderText = "تجاوز الحد",
                    DataPropertyName = "HasExceededCreditLimit",
                    Width = 80
                }
            });

            // Color coding for exceeded credit limit
            _customersGrid.CellFormatting += (sender, e) =>
            {
                if (e.ColumnIndex == _customersGrid.Columns["HasExceededCreditLimit"].Index && 
                    e.Value is bool exceeded && exceeded)
                {
                    e.CellStyle.BackColor = Color.FromArgb(255, 235, 235);
                    e.CellStyle.ForeColor = Color.FromArgb(220, 53, 69);
                }
            };
        }

        private void SetupLayout()
        {
            // Additional layout setup if needed
        }

        private void SetupEvents()
        {
            if (_addButton != null)
                _addButton.Click += AddButton_Click;

            if (_editButton != null)
                _editButton.Click += EditButton_Click;

            if (_deleteButton != null)
                _deleteButton.Click += DeleteButton_Click;

            if (_statementButton != null)
                _statementButton.Click += StatementButton_Click;

            if (_refreshButton != null)
                _refreshButton.Click += RefreshButton_Click;

            if (_searchTextBox != null)
                _searchTextBox.TextChanged += SearchTextBox_TextChanged;

            if (_customersGrid != null)
                _customersGrid.SelectionChanged += CustomersGrid_SelectionChanged;

            if (_customersGrid != null)
                _customersGrid.CellDoubleClick += CustomersGrid_CellDoubleClick;
        }

        private async void LoadCustomersAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;

                var response = await _apiService.GetAsync<List<Customer>>("customers");

                if (response.Success && response.Data != null)
                {
                    _customers = response.Data;
                    PopulateCustomersGrid();
                    UpdateStatistics();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PopulateCustomersGrid()
        {
            if (_customersGrid == null) return;

            var filteredCustomers = _customers.AsEnumerable();

            // Apply filters
            if (_activeOnlyCheckBox?.Checked == true)
                filteredCustomers = filteredCustomers.Where(c => c.IsActive);

            if (_exceededCreditCheckBox?.Checked == true)
                filteredCustomers = filteredCustomers.Where(c => c.HasExceededCreditLimit);

            if (!string.IsNullOrEmpty(_searchTextBox?.Text))
            {
                var searchTerm = _searchTextBox.Text.ToLower();
                filteredCustomers = filteredCustomers.Where(c => 
                    c.Name.ToLower().Contains(searchTerm) ||
                    c.Code.ToLower().Contains(searchTerm) ||
                    c.Phone.ToLower().Contains(searchTerm) ||
                    c.Email.ToLower().Contains(searchTerm));
            }

            var customerTypeValue = (_customerTypeComboBox?.SelectedItem as dynamic)?.Value?.ToString();
            if (!string.IsNullOrEmpty(customerTypeValue))
                filteredCustomers = filteredCustomers.Where(c => c.CustomerType == customerTypeValue);

            _customersGrid.DataSource = filteredCustomers.ToList();
        }

        private void UpdateStatistics()
        {
            if (_totalCustomersLabel != null)
                _totalCustomersLabel.Text = $"إجمالي العملاء: {_customers.Count}";

            if (_totalReceivablesLabel != null)
            {
                var totalReceivables = _customers.Sum(c => c.CurrentBalance);
                _totalReceivablesLabel.Text = $"إجمالي المستحقات: {totalReceivables:N2}";
            }
        }

        // Event handlers
        private void AddButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج إضافة عميل جديد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح نموذج تعديل العميل", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (_customersGrid?.SelectedRows.Count > 0)
            {
                var customer = _customersGrid.SelectedRows[0].DataBoundItem as Customer;
                if (customer != null)
                {
                    var result = MessageBox.Show($"هل تريد حذف العميل '{customer.Name}'؟", 
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        MessageBox.Show("سيتم تنفيذ عملية الحذف", "قيد التطوير", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        private void StatementButton_Click(object? sender, EventArgs e)
        {
            MessageBox.Show("سيتم فتح كشف حساب العميل", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            await LoadCustomersAsync();
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            PopulateCustomersGrid();
        }

        private void CustomersGrid_SelectionChanged(object? sender, EventArgs e)
        {
            var hasSelection = _customersGrid?.SelectedRows.Count > 0;
            if (_editButton != null) _editButton.Enabled = hasSelection;
            if (_deleteButton != null) _deleteButton.Enabled = hasSelection;
            if (_statementButton != null) _statementButton.Enabled = hasSelection;
        }

        private void CustomersGrid_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditButton_Click(sender, e);
            }
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Branch;
use App\Models\ItemCategory;
use App\Models\Unit;
use App\Models\Warehouse;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Item;
use Illuminate\Database\Seeder;

class BasicDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إنشاء الفروع
        $this->createBranches();
        
        // إنشاء فئات الأصناف
        $this->createItemCategories();
        
        // إنشاء الوحدات
        $this->createUnits();
        
        // إنشاء المخازن
        $this->createWarehouses();
        
        // إنشاء عملاء تجريبيين
        $this->createSampleCustomers();
        
        // إنشاء موردين تجريبيين
        $this->createSampleSuppliers();
        
        // إنشاء أصناف تجريبية
        $this->createSampleItems();

        $this->command->info('تم إنشاء البيانات الأساسية بنجاح!');
    }

    /**
     * إنشاء الفروع
     */
    protected function createBranches()
    {
        $branches = [
            [
                'name' => 'الفرع الرئيسي',
                'name_en' => 'Main Branch',
                'code' => 'MAIN',
                'address' => 'الرياض، المملكة العربية السعودية',
                'phone' => '+966112345678',
                'email' => '<EMAIL>',
                'manager_name' => 'أحمد محمد',
            ],
            [
                'name' => 'فرع جدة',
                'name_en' => 'Jeddah Branch',
                'code' => 'JED',
                'address' => 'جدة، المملكة العربية السعودية',
                'phone' => '+966122345678',
                'email' => '<EMAIL>',
                'manager_name' => 'محمد علي',
            ],
        ];

        foreach ($branches as $branch) {
            Branch::firstOrCreate(['code' => $branch['code']], $branch);
        }
    }

    /**
     * إنشاء فئات الأصناف
     */
    protected function createItemCategories()
    {
        $categories = [
            [
                'name' => 'الإلكترونيات',
                'name_en' => 'Electronics',
                'description' => 'الأجهزة الإلكترونية والكهربائية',
            ],
            [
                'name' => 'الملابس',
                'name_en' => 'Clothing',
                'description' => 'الملابس والأزياء',
            ],
            [
                'name' => 'المواد الغذائية',
                'name_en' => 'Food Items',
                'description' => 'المواد الغذائية والمشروبات',
            ],
            [
                'name' => 'مستلزمات المكتب',
                'name_en' => 'Office Supplies',
                'description' => 'مستلزمات المكاتب والقرطاسية',
            ],
        ];

        foreach ($categories as $category) {
            ItemCategory::firstOrCreate(['name' => $category['name']], $category);
        }

        // إنشاء فئات فرعية
        $electronicsCategory = ItemCategory::where('name', 'الإلكترونيات')->first();
        if ($electronicsCategory) {
            $subCategories = [
                ['name' => 'الهواتف الذكية', 'name_en' => 'Smartphones'],
                ['name' => 'أجهزة الكمبيوتر', 'name_en' => 'Computers'],
                ['name' => 'الأجهزة المنزلية', 'name_en' => 'Home Appliances'],
            ];

            foreach ($subCategories as $subCategory) {
                ItemCategory::firstOrCreate(
                    ['name' => $subCategory['name']],
                    array_merge($subCategory, ['parent_id' => $electronicsCategory->id])
                );
            }
        }
    }

    /**
     * إنشاء الوحدات
     */
    protected function createUnits()
    {
        $units = [
            ['name' => 'قطعة', 'name_en' => 'Piece', 'symbol' => 'قطعة'],
            ['name' => 'كيلوجرام', 'name_en' => 'Kilogram', 'symbol' => 'كجم'],
            ['name' => 'متر', 'name_en' => 'Meter', 'symbol' => 'م'],
            ['name' => 'لتر', 'name_en' => 'Liter', 'symbol' => 'لتر'],
            ['name' => 'علبة', 'name_en' => 'Box', 'symbol' => 'علبة'],
            ['name' => 'كرتون', 'name_en' => 'Carton', 'symbol' => 'كرتون'],
            ['name' => 'دزينة', 'name_en' => 'Dozen', 'symbol' => 'دزينة'],
        ];

        foreach ($units as $unit) {
            Unit::firstOrCreate(['name' => $unit['name']], $unit);
        }
    }

    /**
     * إنشاء المخازن
     */
    protected function createWarehouses()
    {
        $mainBranch = Branch::where('code', 'MAIN')->first();
        $jeddahBranch = Branch::where('code', 'JED')->first();

        $warehouses = [
            [
                'name' => 'المخزن الرئيسي',
                'name_en' => 'Main Warehouse',
                'code' => 'WH0001',
                'location' => 'الرياض - المنطقة الصناعية',
                'manager_name' => 'خالد أحمد',
                'phone' => '+966112345679',
                'branch_id' => $mainBranch->id,
            ],
            [
                'name' => 'مخزن جدة',
                'name_en' => 'Jeddah Warehouse',
                'code' => 'WH0002',
                'location' => 'جدة - المنطقة الصناعية',
                'manager_name' => 'عبدالله محمد',
                'phone' => '+966122345679',
                'branch_id' => $jeddahBranch->id,
            ],
        ];

        foreach ($warehouses as $warehouse) {
            Warehouse::firstOrCreate(['code' => $warehouse['code']], $warehouse);
        }
    }

    /**
     * إنشاء عملاء تجريبيين
     */
    protected function createSampleCustomers()
    {
        $customers = [
            [
                'code' => 'CUST000001',
                'name' => 'شركة الأمل للتجارة',
                'name_en' => 'Al Amal Trading Company',
                'phone' => '+966501234567',
                'email' => '<EMAIL>',
                'address' => 'الرياض، حي العليا',
                'tax_number' => '300123456789003',
                'credit_limit' => 50000,
                'payment_terms' => 30,
                'customer_type' => 'company',
                'opening_balance' => 15000,
                'current_balance' => 15000,
            ],
            [
                'code' => 'CUST000002',
                'name' => 'أحمد محمد العلي',
                'name_en' => 'Ahmed Mohammed Al Ali',
                'phone' => '+966502345678',
                'email' => '<EMAIL>',
                'address' => 'جدة، حي الروضة',
                'credit_limit' => 10000,
                'payment_terms' => 15,
                'customer_type' => 'individual',
                'opening_balance' => 2500,
                'current_balance' => 2500,
            ],
            [
                'code' => 'CUST000003',
                'name' => 'مؤسسة النور للمقاولات',
                'name_en' => 'Al Noor Contracting Est.',
                'phone' => '+966503456789',
                'email' => '<EMAIL>',
                'address' => 'الدمام، حي الفيصلية',
                'tax_number' => '300234567890003',
                'credit_limit' => 100000,
                'payment_terms' => 45,
                'customer_type' => 'company',
                'opening_balance' => 0,
                'current_balance' => 0,
            ],
        ];

        foreach ($customers as $customer) {
            Customer::firstOrCreate(['code' => $customer['code']], $customer);
        }
    }

    /**
     * إنشاء موردين تجريبيين
     */
    protected function createSampleSuppliers()
    {
        $suppliers = [
            [
                'code' => 'SUPP000001',
                'name' => 'شركة التقنية المتقدمة',
                'name_en' => 'Advanced Technology Co.',
                'phone' => '+966511234567',
                'email' => '<EMAIL>',
                'address' => 'الرياض، حي الملز',
                'tax_number' => '300345678901003',
                'payment_terms' => 30,
                'opening_balance' => 25000,
                'current_balance' => 25000,
            ],
            [
                'code' => 'SUPP000002',
                'name' => 'مؤسسة الجودة للاستيراد',
                'name_en' => 'Quality Import Est.',
                'phone' => '+966512345678',
                'email' => '<EMAIL>',
                'address' => 'جدة، حي البلد',
                'tax_number' => '300456789012003',
                'payment_terms' => 45,
                'opening_balance' => 18000,
                'current_balance' => 18000,
            ],
        ];

        foreach ($suppliers as $supplier) {
            Supplier::firstOrCreate(['code' => $supplier['code']], $supplier);
        }
    }

    /**
     * إنشاء أصناف تجريبية
     */
    protected function createSampleItems()
    {
        $electronicsCategory = ItemCategory::where('name', 'الإلكترونيات')->first();
        $smartphoneCategory = ItemCategory::where('name', 'الهواتف الذكية')->first();
        $clothingCategory = ItemCategory::where('name', 'الملابس')->first();
        $foodCategory = ItemCategory::where('name', 'المواد الغذائية')->first();

        $pieceUnit = Unit::where('name', 'قطعة')->first();
        $kgUnit = Unit::where('name', 'كيلوجرام')->first();

        $items = [
            [
                'code' => 'ITEM000001',
                'name' => 'هاتف ذكي سامسونج جالاكسي',
                'name_en' => 'Samsung Galaxy Smartphone',
                'category_id' => $smartphoneCategory->id,
                'unit_id' => $pieceUnit->id,
                'cost_price' => 1200,
                'selling_price' => 1500,
                'min_stock_level' => 10,
                'max_stock_level' => 100,
                'is_taxable' => true,
                'tax_type_id' => 1, // VAT
            ],
            [
                'code' => 'ITEM000002',
                'name' => 'قميص قطني رجالي',
                'name_en' => 'Men Cotton Shirt',
                'category_id' => $clothingCategory->id,
                'unit_id' => $pieceUnit->id,
                'cost_price' => 80,
                'selling_price' => 120,
                'min_stock_level' => 20,
                'max_stock_level' => 200,
                'is_taxable' => true,
                'tax_type_id' => 1, // VAT
            ],
            [
                'code' => 'ITEM000003',
                'name' => 'أرز بسمتي',
                'name_en' => 'Basmati Rice',
                'category_id' => $foodCategory->id,
                'unit_id' => $kgUnit->id,
                'cost_price' => 8,
                'selling_price' => 12,
                'min_stock_level' => 100,
                'max_stock_level' => 1000,
                'is_taxable' => false, // معفى من الضريبة
                'tax_type_id' => 6, // TAX_EXEMPT
            ],
        ];

        foreach ($items as $item) {
            Item::firstOrCreate(['code' => $item['code']], $item);
        }
    }
}

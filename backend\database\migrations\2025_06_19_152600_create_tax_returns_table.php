<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_returns', function (Blueprint $table) {
            $table->id();
            $table->string('return_number')->unique(); // رقم الإقرار
            $table->enum('return_type', ['vat', 'withholding', 'excise', 'custom']); // نوع الإقرار
            $table->enum('period_type', ['monthly', 'quarterly', 'annual']); // نوع الفترة
            $table->date('period_start'); // بداية الفترة
            $table->date('period_end'); // نهاية الفترة
            $table->date('due_date'); // تاريخ الاستحقاق
            $table->date('submission_date')->nullable(); // تاريخ التقديم
            $table->enum('status', ['draft', 'calculated', 'submitted', 'approved', 'rejected']);
            
            // مبالغ الإقرار
            $table->decimal('total_sales', 15, 2)->default(0); // إجمالي المبيعات
            $table->decimal('taxable_sales', 15, 2)->default(0); // المبيعات الخاضعة
            $table->decimal('exempt_sales', 15, 2)->default(0); // المبيعات المعفاة
            $table->decimal('output_tax', 15, 2)->default(0); // ضريبة المخرجات
            $table->decimal('input_tax', 15, 2)->default(0); // ضريبة المدخلات
            $table->decimal('net_tax', 15, 2)->default(0); // صافي الضريبة
            $table->decimal('adjustments', 15, 2)->default(0); // التسويات
            $table->decimal('penalties', 15, 2)->default(0); // الغرامات
            $table->decimal('total_due', 15, 2)->default(0); // إجمالي المستحق
            
            $table->json('detailed_breakdown')->nullable(); // التفصيل الكامل
            $table->json('attachments')->nullable(); // المرفقات
            $table->text('notes')->nullable();
            $table->foreignId('prepared_by')->constrained('users')->onDelete('restrict');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_returns');
    }
};

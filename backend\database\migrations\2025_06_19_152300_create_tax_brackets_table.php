<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_brackets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tax_type_id')->constrained()->onDelete('cascade');
            $table->string('bracket_name'); // اسم الشريحة
            $table->decimal('min_amount', 15, 2)->default(0); // الحد الأدنى للشريحة
            $table->decimal('max_amount', 15, 2)->nullable(); // الحد الأقصى للشريحة (null = لا نهاية)
            $table->decimal('rate', 8, 4); // معدل الضريبة للشريحة
            $table->decimal('fixed_amount', 15, 2)->default(0); // مبلغ ثابت إضافي
            $table->enum('calculation_method', ['progressive', 'flat'])->default('progressive');
            $table->integer('order')->default(1); // ترتيب الشريحة
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_brackets');
    }
};

{"name": "illuminate/auth", "description": "The Illuminate Auth package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-hash": "*", "illuminate/collections": "^9.0", "illuminate/contracts": "^9.0", "illuminate/http": "^9.0", "illuminate/macroable": "^9.0", "illuminate/queue": "^9.0", "illuminate/support": "^9.0"}, "autoload": {"psr-4": {"Illuminate\\Auth\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"illuminate/console": "Required to use the auth:clear-resets command (^9.0).", "illuminate/queue": "Required to fire login / logout events (^9.0).", "illuminate/session": "Required to use the session based guard (^9.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
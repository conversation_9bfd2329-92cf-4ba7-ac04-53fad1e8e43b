{"name": "illuminate/notifications", "description": "The Illuminate Notifications package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "illuminate/broadcasting": "^9.0", "illuminate/bus": "^9.0", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/filesystem": "^9.0", "illuminate/mail": "^9.0", "illuminate/queue": "^9.0", "illuminate/support": "^9.0"}, "autoload": {"psr-4": {"Illuminate\\Notifications\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"illuminate/database": "Required to use the database transport (^9.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
# 📊 نظام المحاسبة المتكامل - دليل شامل

## 🎯 نظرة عامة

تم تطوير نظام محاسبة متكامل وشامل يغطي جميع احتياجات الشركات الحديثة في المحاسبة والمبيعات والمشتريات وإدارة المخزون. النظام مبني بتقنيات حديثة ومتوافق مع المعايير المحاسبية السعودية والخليجية.

## 🏗️ هيكل النظام

### Backend (Laravel API)
- **Framework**: Laravel 10
- **Database**: MySQL 8.0
- **Authentication**: Laravel Sanctum
- **Authorization**: Spatie Laravel Permission

### Frontend (C# Desktop App)
- **Framework**: .NET 6 WinForms/WPF
- **UI Library**: Modern UI Components
- **HTTP Client**: HttpClient for API communication

## 📋 الوحدات الرئيسية

### 1. 🔐 نظام المصادقة والصلاحيات
- تسجيل الدخول والخروج
- إدارة المستخدمين
- الأدوار والصلاحيات
- حماية API endpoints

### 2. 🧾 النظام الضريبي المتقدم
- **أنواع الضرائب**: 10 أنواع مختلفة
- **المجموعات الضريبية**: للضرائب المركبة
- **الإعفاءات الضريبية**: كاملة وجزئية ومشروطة
- **الشرائح الضريبية**: للضرائب التصاعدية
- **القواعد الضريبية**: للحالات المعقدة
- **التقارير الضريبية**: متوافقة مع المتطلبات الحكومية

### 3. 📚 دليل الحسابات
- **الحسابات الرئيسية**: الأصول، الخصوم، حقوق الملكية، الإيرادات، المصروفات
- **الحسابات الفرعية**: هيكل شجري متعدد المستويات
- **أرصدة الحسابات**: افتتاحية وجارية
- **كشوف الحسابات**: تفصيلية مع الحركات

### 4. 📝 القيود المحاسبية
- **إنشاء القيود**: مع التحقق من التوازن
- **اعتماد القيود**: نظام موافقات
- **عكس القيود**: للتصحيحات
- **تقارير القيود**: شاملة ومفصلة

### 5. 🏢 مراكز التكلفة
- **إدارة المراكز**: هيكل شجري
- **تقارير المراكز**: تحليل التكاليف
- **ربط بالقيود**: تتبع دقيق للتكاليف

### 6. 👥 إدارة العملاء
- **بيانات العملاء**: شاملة ومفصلة
- **الحدود الائتمانية**: مراقبة وتنبيهات
- **كشوف الحسابات**: تفصيلية
- **تحليل الأعمار**: للديون المستحقة
- **التقارير**: متنوعة وشاملة

### 7. 🏭 إدارة الموردين
- **بيانات الموردين**: كاملة
- **شروط الدفع**: مرنة
- **كشوف الحسابات**: مفصلة
- **تقارير الموردين**: أداء وإحصائيات

### 8. 📦 إدارة المخزون
- **الأصناف**: تصنيف متقدم
- **المخازن**: متعددة الفروع
- **حركات المخزون**: تتبع دقيق
- **تقييم المخزون**: طرق متعددة
- **التقارير**: شاملة ومفصلة

### 9. 📊 التقارير المالية
- **قائمة الدخل**: تفصيلية
- **الميزانية العمومية**: متوازنة
- **قائمة التدفقات النقدية**: شاملة
- **النسب المالية**: تحليلية
- **التقارير المقارنة**: بين الفترات

## 🔧 الميزات المتقدمة

### ✅ النظام الضريبي
```php
// حساب ضريبة مركبة
$taxResult = $taxService->calculateAdvancedLineItemTax(
    $item, $quantity, $unitPrice, $discount, $context
);

// النتيجة تشمل:
// - الضريبة الأساسية
// - الضرائب المركبة
// - الإعفاءات المطبقة
// - التفاصيل الكاملة
```

### ✅ المحاسبة المتقدمة
```php
// إنشاء قيد محاسبي
$entry = JournalEntry::create([
    'description' => 'قيد مبيعات',
    'details' => [
        ['account_id' => 1, 'debit' => 1000, 'credit' => 0],
        ['account_id' => 2, 'debit' => 0, 'credit' => 1000],
    ]
]);

// اعتماد القيد
$entry->approve();
```

### ✅ إدارة المخزون الذكية
```php
// تحديث المخزون تلقائياً
$warehouse->updateStock($itemId, $quantity, 'in', $reference);

// نقل بين المخازن
$warehouse->transferStock($itemId, $fromWarehouse, $toWarehouse, $quantity);

// تنبيهات المخزون المنخفض
$lowStockItems = $warehouse->getLowStockItems();
```

## 📊 API Endpoints

### المصادقة
```
POST   /api/auth/login           # تسجيل الدخول
POST   /api/auth/logout          # تسجيل الخروج
GET    /api/auth/user            # بيانات المستخدم
```

### الضرائب
```
GET    /api/tax-types            # أنواع الضرائب
POST   /api/tax-types/calculate  # حساب الضريبة
GET    /api/tax-reports/vat      # تقرير ضريبة القيمة المضافة
```

### المحاسبة
```
GET    /api/accounts             # دليل الحسابات
POST   /api/journal-entries      # إنشاء قيد
GET    /api/financial-reports/income-statement  # قائمة الدخل
```

### العملاء والموردين
```
GET    /api/customers            # قائمة العملاء
GET    /api/suppliers            # قائمة الموردين
GET    /api/customers/{id}/statement  # كشف حساب عميل
```

### المخزون
```
GET    /api/inventory/current-stock     # المخزون الحالي
POST   /api/inventory/adjust-stock     # تعديل المخزون
GET    /api/inventory/low-stock-report # تقرير المخزون المنخفض
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية
- **users**: المستخدمين
- **accounts**: دليل الحسابات
- **journal_entries**: القيود المحاسبية
- **customers**: العملاء
- **suppliers**: الموردين
- **items**: الأصناف
- **warehouses**: المخازن
- **tax_types**: أنواع الضرائب

### العلاقات
- علاقات معقدة ومترابطة
- فهارس محسنة للأداء
- قيود المرجعية للحفاظ على سلامة البيانات

## 🔒 الأمان

### حماية API
- Laravel Sanctum للمصادقة
- Middleware للتحقق من الصلاحيات
- Rate limiting لمنع الهجمات

### حماية البيانات
- تشفير كلمات المرور
- حماية من SQL Injection
- التحقق من صحة البيانات

## 📈 الأداء

### تحسينات قاعدة البيانات
- فهارس محسنة
- استعلامات محسنة
- Eager loading للعلاقات

### تحسينات API
- Pagination للقوائم الطويلة
- Caching للبيانات المتكررة
- Response optimization

## 🧪 الاختبار

### اختبارات Backend
```bash
# تشغيل الاختبارات
php artisan test

# اختبارات محددة
php artisan test --filter TaxCalculationTest
```

### اختبارات Frontend
- Unit tests للمكونات
- Integration tests للـ API
- UI tests للواجهات

## 🚀 النشر

### متطلبات الخادم
- PHP 8.1+
- MySQL 8.0+
- Composer
- Node.js (للـ assets)

### خطوات النشر
```bash
# استنساخ المشروع
git clone [repository-url]

# تثبيت التبعيات
composer install

# إعداد البيئة
cp .env.example .env
php artisan key:generate

# إعداد قاعدة البيانات
php artisan migrate --seed

# تشغيل الخادم
php artisan serve
```

## 📚 التوثيق الإضافي

- [دليل النظام الضريبي المتقدم](./advanced-tax-system.md)
- [دليل API](./api-documentation.md)
- [دليل قاعدة البيانات](./database-schema.md)
- [دليل النشر](./deployment-guide.md)

## 🆘 الدعم الفني

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +************
- الموقع: www.company.com/support

---

**تم تطوير هذا النظام بأحدث التقنيات ووفقاً لأفضل الممارسات في البرمجة والمحاسبة.**

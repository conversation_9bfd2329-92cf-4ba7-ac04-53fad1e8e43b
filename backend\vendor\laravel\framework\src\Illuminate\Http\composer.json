{"name": "illuminate/http", "description": "The Illuminate Http package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-filter": "*", "fruitcake/php-cors": "^1.2", "guzzlehttp/uri-template": "^1.0", "illuminate/collections": "^9.0", "illuminate/macroable": "^9.0", "illuminate/session": "^9.0", "illuminate/support": "^9.0", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0", "symfony/mime": "^6.0"}, "autoload": {"psr-4": {"Illuminate\\Http\\": ""}}, "suggest": {"ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "guzzlehttp/guzzle": "Required to use the HTTP Client (^7.5)."}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
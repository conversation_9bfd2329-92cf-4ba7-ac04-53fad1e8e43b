<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'phone',
        'email',
        'address',
        'tax_number',
        'credit_limit',
        'payment_terms',
        'customer_type',
        'opening_balance',
        'current_balance',
        'is_active',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'credit_limit' => 'decimal:2',
        'payment_terms' => 'integer',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the sales invoices for this customer.
     */
    public function salesInvoices()
    {
        return $this->hasMany(SalesInvoice::class);
    }

    /**
     * Get the payments received from this customer.
     */
    public function payments()
    {
        return $this->hasMany(CustomerPayment::class);
    }

    /**
     * Scope a query to only include active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by customer type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('customer_type', $type);
    }

    /**
     * Check if customer has exceeded credit limit.
     */
    public function hasExceededCreditLimit()
    {
        return $this->current_balance > $this->credit_limit;
    }

    /**
     * Get available credit.
     */
    public function getAvailableCredit()
    {
        return max(0, $this->credit_limit - $this->current_balance);
    }

    /**
     * Update customer balance.
     */
    public function updateBalance($amount, $type = 'debit')
    {
        if ($type === 'debit') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    /**
     * Get customer statement.
     */
    public function getStatement($fromDate = null, $toDate = null)
    {
        $fromDate = $fromDate ?? now()->startOfMonth()->toDateString();
        $toDate = $toDate ?? now()->toDateString();

        $invoices = $this->salesInvoices()
                        ->whereBetween('invoice_date', [$fromDate, $toDate])
                        ->where('status', 'approved')
                        ->orderBy('invoice_date')
                        ->get();

        $payments = $this->payments()
                        ->whereBetween('payment_date', [$fromDate, $toDate])
                        ->orderBy('payment_date')
                        ->get();

        // دمج الفواتير والمدفوعات وترتيبها حسب التاريخ
        $transactions = collect();

        foreach ($invoices as $invoice) {
            $transactions->push([
                'date' => $invoice->invoice_date,
                'type' => 'invoice',
                'reference' => $invoice->invoice_number,
                'description' => 'فاتورة مبيعات',
                'debit' => $invoice->total_amount,
                'credit' => 0,
                'data' => $invoice,
            ]);
        }

        foreach ($payments as $payment) {
            $transactions->push([
                'date' => $payment->payment_date,
                'type' => 'payment',
                'reference' => $payment->payment_number,
                'description' => 'دفعة من العميل',
                'debit' => 0,
                'credit' => $payment->amount,
                'data' => $payment,
            ]);
        }

        $transactions = $transactions->sortBy('date')->values();

        // حساب الرصيد الجاري
        $runningBalance = $this->opening_balance;
        foreach ($transactions as $key => $transaction) {
            $runningBalance += $transaction['debit'] - $transaction['credit'];
            $transactions[$key]['running_balance'] = $runningBalance;
        }

        return [
            'customer' => $this,
            'period' => ['from' => $fromDate, 'to' => $toDate],
            'opening_balance' => $this->opening_balance,
            'closing_balance' => $runningBalance,
            'transactions' => $transactions,
            'summary' => [
                'total_invoices' => $invoices->sum('total_amount'),
                'total_payments' => $payments->sum('amount'),
                'net_change' => $invoices->sum('total_amount') - $payments->sum('amount'),
            ],
        ];
    }

    /**
     * Generate unique customer code.
     */
    public static function generateCustomerCode()
    {
        $lastCustomer = static::orderBy('code', 'desc')->first();
        
        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->code, 4); // Remove "CUST" prefix
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return 'CUST' . str_pad($newNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Get overdue invoices.
     */
    public function getOverdueInvoices()
    {
        return $this->salesInvoices()
                   ->where('status', 'approved')
                   ->where('payment_status', '!=', 'paid')
                   ->whereRaw('DATE_ADD(invoice_date, INTERVAL payment_terms DAY) < ?', [now()->toDateString()])
                   ->get();
    }

    /**
     * Calculate aging analysis.
     */
    public function getAgingAnalysis()
    {
        $overdueInvoices = $this->getOverdueInvoices();
        $today = now();

        $aging = [
            'current' => 0,      // 0-30 days
            '30_days' => 0,      // 31-60 days
            '60_days' => 0,      // 61-90 days
            '90_days_plus' => 0, // 90+ days
        ];

        foreach ($overdueInvoices as $invoice) {
            $dueDate = $invoice->invoice_date->addDays($this->payment_terms);
            $daysOverdue = $today->diffInDays($dueDate);
            $remainingAmount = $invoice->total_amount - $invoice->paid_amount;

            if ($daysOverdue <= 30) {
                $aging['current'] += $remainingAmount;
            } elseif ($daysOverdue <= 60) {
                $aging['30_days'] += $remainingAmount;
            } elseif ($daysOverdue <= 90) {
                $aging['60_days'] += $remainingAmount;
            } else {
                $aging['90_days_plus'] += $remainingAmount;
            }
        }

        return $aging;
    }
}

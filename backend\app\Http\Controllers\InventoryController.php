<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Warehouse;
use App\Models\InventoryItem;
use App\Models\StockMovement;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InventoryController extends Controller
{
    /**
     * عرض تقرير المخزون الحالي
     */
    public function currentStock(Request $request)
    {
        $query = InventoryItem::with(['item.category', 'item.unit', 'warehouse']);

        // فلترة حسب المخزن
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // فلترة حسب الفئة
        if ($request->has('category_id')) {
            $query->whereHas('item', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        // فلترة المخزون المنخفض
        if ($request->boolean('low_stock_only')) {
            $query->whereHas('item', function ($q) {
                $q->whereColumn('current_stock', '<=', 'min_stock_level');
            });
        }

        // فلترة المخزون المنتهي
        if ($request->boolean('out_of_stock_only')) {
            $query->where('current_stock', '<=', 0);
        }

        $inventoryItems = $query->orderBy('current_stock')->paginate(50);

        return response()->json([
            'success' => true,
            'data' => $inventoryItems,
        ]);
    }

    /**
     * حركات المخزون
     */
    public function stockMovements(Request $request)
    {
        $query = StockMovement::with(['item', 'warehouse', 'user']);

        // فلترة حسب الصنف
        if ($request->has('item_id')) {
            $query->where('item_id', $request->item_id);
        }

        // فلترة حسب المخزن
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // فلترة حسب نوع الحركة
        if ($request->has('movement_type')) {
            $query->where('movement_type', $request->movement_type);
        }

        // فلترة حسب التاريخ
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $movements = $query->orderBy('created_at', 'desc')->paginate(50);

        return response()->json([
            'success' => true,
            'data' => $movements,
        ]);
    }

    /**
     * تعديل المخزون يدوياً
     */
    public function adjustStock(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:items,id',
            'warehouse_id' => 'required|exists:warehouses,id',
            'adjustment_type' => 'required|in:increase,decrease,set',
            'quantity' => 'required|numeric|min:0',
            'reason' => 'required|string|max:255',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $warehouse = Warehouse::findOrFail($request->warehouse_id);
            $item = Item::findOrFail($request->item_id);
            
            $inventoryItem = $warehouse->inventoryItems()
                                     ->firstOrCreate(
                                         ['item_id' => $request->item_id],
                                         ['current_stock' => 0, 'reserved_stock' => 0]
                                     );

            $oldStock = $inventoryItem->current_stock;
            $newStock = $oldStock;

            switch ($request->adjustment_type) {
                case 'increase':
                    $newStock = $oldStock + $request->quantity;
                    $movementType = 'in';
                    $movementQuantity = $request->quantity;
                    break;
                    
                case 'decrease':
                    $newStock = max(0, $oldStock - $request->quantity);
                    $movementType = 'out';
                    $movementQuantity = $request->quantity;
                    break;
                    
                case 'set':
                    $newStock = $request->quantity;
                    $movementType = $newStock > $oldStock ? 'in' : 'out';
                    $movementQuantity = abs($newStock - $oldStock);
                    break;
            }

            // تحديث المخزون
            $inventoryItem->update(['current_stock' => $newStock]);

            // تسجيل حركة المخزون
            $warehouse->stockMovements()->create([
                'item_id' => $request->item_id,
                'movement_type' => $movementType,
                'quantity' => $movementQuantity,
                'reference_type' => 'manual_adjustment',
                'reference_id' => null,
                'notes' => "تعديل يدوي: {$request->reason}. {$request->notes}",
                'user_id' => auth()->id(),
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم تعديل المخزون بنجاح',
                'data' => [
                    'item' => $item,
                    'warehouse' => $warehouse,
                    'old_stock' => $oldStock,
                    'new_stock' => $newStock,
                    'adjustment' => $newStock - $oldStock,
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تعديل المخزون',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * نقل المخزون بين المخازن
     */
    public function transferStock(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:items,id',
            'from_warehouse_id' => 'required|exists:warehouses,id',
            'to_warehouse_id' => 'required|exists:warehouses,id|different:from_warehouse_id',
            'quantity' => 'required|numeric|min:0.01',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $fromWarehouse = Warehouse::findOrFail($request->from_warehouse_id);
            $toWarehouse = Warehouse::findOrFail($request->to_warehouse_id);
            $item = Item::findOrFail($request->item_id);

            // التحقق من توفر المخزون
            $currentStock = $fromWarehouse->getCurrentStock($request->item_id);
            if ($currentStock < $request->quantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'المخزون المتاح غير كافي للنقل',
                    'available_stock' => $currentStock,
                ], 422);
            }

            // خصم من المخزن المصدر
            $fromWarehouse->updateStock(
                $request->item_id,
                $request->quantity,
                'out',
                ['type' => 'transfer', 'id' => null],
                "نقل إلى مخزن {$toWarehouse->name}. {$request->notes}"
            );

            // إضافة للمخزن المستهدف
            $toWarehouse->updateStock(
                $request->item_id,
                $request->quantity,
                'in',
                ['type' => 'transfer', 'id' => null],
                "نقل من مخزن {$fromWarehouse->name}. {$request->notes}"
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم نقل المخزون بنجاح',
                'data' => [
                    'item' => $item,
                    'from_warehouse' => $fromWarehouse,
                    'to_warehouse' => $toWarehouse,
                    'quantity' => $request->quantity,
                    'remaining_stock' => $fromWarehouse->getCurrentStock($request->item_id),
                    'new_stock' => $toWarehouse->getCurrentStock($request->item_id),
                ],
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء نقل المخزون',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تقرير المخزون المنخفض
     */
    public function lowStockReport(Request $request)
    {
        $query = InventoryItem::with(['item.category', 'item.unit', 'warehouse'])
                             ->whereHas('item', function ($q) {
                                 $q->whereColumn('current_stock', '<=', 'min_stock_level');
                             });

        // فلترة حسب المخزن
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        $lowStockItems = $query->orderBy('current_stock')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $lowStockItems,
                'total_items' => $lowStockItems->count(),
                'total_value' => $lowStockItems->sum(function ($inventoryItem) {
                    return $inventoryItem->current_stock * $inventoryItem->item->cost_price;
                }),
            ],
        ]);
    }

    /**
     * تقرير قيمة المخزون
     */
    public function stockValuationReport(Request $request)
    {
        $query = InventoryItem::with(['item.category', 'warehouse'])
                             ->where('current_stock', '>', 0);

        // فلترة حسب المخزن
        if ($request->has('warehouse_id')) {
            $query->where('warehouse_id', $request->warehouse_id);
        }

        // فلترة حسب الفئة
        if ($request->has('category_id')) {
            $query->whereHas('item', function ($q) use ($request) {
                $q->where('category_id', $request->category_id);
            });
        }

        $inventoryItems = $query->get();

        $valuation = $inventoryItems->map(function ($inventoryItem) {
            return [
                'item' => $inventoryItem->item,
                'warehouse' => $inventoryItem->warehouse,
                'current_stock' => $inventoryItem->current_stock,
                'cost_price' => $inventoryItem->item->cost_price,
                'total_value' => $inventoryItem->current_stock * $inventoryItem->item->cost_price,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $valuation,
                'summary' => [
                    'total_items' => $valuation->count(),
                    'total_quantity' => $valuation->sum('current_stock'),
                    'total_value' => $valuation->sum('total_value'),
                ],
            ],
        ]);
    }

    /**
     * إحصائيات المخزون
     */
    public function getStatistics(Request $request)
    {
        $warehouseId = $request->warehouse_id;

        $query = InventoryItem::query();
        if ($warehouseId) {
            $query->where('warehouse_id', $warehouseId);
        }

        $totalItems = $query->count();
        $totalValue = $query->with('item')->get()->sum(function ($inventoryItem) {
            return $inventoryItem->current_stock * $inventoryItem->item->cost_price;
        });

        $lowStockItems = $query->whereHas('item', function ($q) {
            $q->whereColumn('current_stock', '<=', 'min_stock_level');
        })->count();

        $outOfStockItems = $query->where('current_stock', '<=', 0)->count();

        return response()->json([
            'success' => true,
            'data' => [
                'total_items' => $totalItems,
                'total_value' => $totalValue,
                'low_stock_items' => $lowStockItems,
                'out_of_stock_items' => $outOfStockItems,
                'stock_turnover' => 0, // يحتاج حساب معقد
            ],
        ]);
    }
}

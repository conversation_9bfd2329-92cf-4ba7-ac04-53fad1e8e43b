<?php

namespace App\Services;

use App\Models\Item;
use App\Models\TaxType;
use App\Models\TaxGroup;
use App\Models\TaxExemption;
use App\Models\TaxRule;
use App\Models\TaxBracket;
use App\Models\TaxCalculation;
use Illuminate\Support\Facades\DB;

class AdvancedTaxCalculationService
{
    /**
     * Calculate comprehensive tax for an invoice line item.
     */
    public function calculateAdvancedLineItemTax($item, $quantity, $unitPrice, $discountAmount = 0, $context = [])
    {
        if (!$item instanceof Item) {
            $item = Item::find($item);
        }

        if (!$item || !$item->is_taxable) {
            return $this->createTaxResult(0, 0, ($unitPrice * $quantity) - $discountAmount, []);
        }

        $netAmount = ($unitPrice * $quantity) - $discountAmount;
        $taxCalculations = [];
        $totalTax = 0;
        $exemptionAmount = 0;

        // Get applicable tax group or individual tax type
        if ($item->tax_group_id) {
            $result = $this->calculateGroupTax($item->taxGroup, $netAmount, $context);
            $totalTax = $result['total_tax'];
            $taxCalculations = $result['breakdown'];
        } elseif ($item->tax_type_id) {
            $result = $this->calculateSingleTax($item->taxType, $netAmount, $context);
            $totalTax = $result['tax_amount'];
            $taxCalculations = [$result];
        }

        // Apply exemptions
        if ($item->tax_exemptions) {
            $exemptionResult = $this->applyExemptions($item->tax_exemptions, $totalTax, $netAmount, $context);
            $exemptionAmount = $exemptionResult['total_exemption'];
            $totalTax -= $exemptionAmount;
        }

        // Apply tax rules
        $rulesResult = $this->applyTaxRules($item, $netAmount, $totalTax, $context);
        $totalTax = $rulesResult['adjusted_tax'];

        // Store calculation record
        $this->storeTaxCalculation($item, 'item', $netAmount, $totalTax, $taxCalculations, $context);

        return $this->createTaxResult($totalTax, $exemptionAmount, $netAmount, $taxCalculations);
    }

    /**
     * Calculate tax using tax group.
     */
    protected function calculateGroupTax($taxGroup, $amount, $context = [])
    {
        if (!$taxGroup) {
            return ['total_tax' => 0, 'breakdown' => []];
        }

        return $taxGroup->calculateGroupTax($amount, $context);
    }

    /**
     * Calculate tax using single tax type.
     */
    protected function calculateSingleTax($taxType, $amount, $context = [])
    {
        if (!$taxType) {
            return ['tax_amount' => 0, 'breakdown' => []];
        }

        // Check for bracket-based calculation
        $brackets = $taxType->taxBrackets()->active()->orderBy('order')->get();
        
        if ($brackets->isNotEmpty()) {
            return $this->calculateBracketTax($brackets, $amount);
        }

        // Standard calculation
        $taxAmount = $taxType->calculateTax($amount);

        return [
            'tax_type_id' => $taxType->id,
            'tax_type_name' => $taxType->name,
            'base_amount' => $amount,
            'tax_rate' => $taxType->rate,
            'tax_amount' => $taxAmount,
            'calculation_method' => $taxType->calculation_method,
        ];
    }

    /**
     * Calculate tax using bracket system.
     */
    protected function calculateBracketTax($brackets, $amount)
    {
        $totalTax = 0;
        $remainingAmount = $amount;
        $bracketDetails = [];

        foreach ($brackets as $bracket) {
            if ($remainingAmount <= 0) {
                break;
            }

            $bracketMin = $bracket->min_amount;
            $bracketMax = $bracket->max_amount ?? PHP_FLOAT_MAX;
            
            if ($amount <= $bracketMin) {
                continue;
            }

            $taxableInBracket = min($remainingAmount, $bracketMax - $bracketMin);
            
            if ($bracket->calculation_method === 'progressive') {
                $bracketTax = ($taxableInBracket * $bracket->rate / 100) + $bracket->fixed_amount;
                $remainingAmount -= $taxableInBracket;
            } else {
                // Flat rate for entire amount
                $bracketTax = ($amount * $bracket->rate / 100) + $bracket->fixed_amount;
                $remainingAmount = 0;
            }

            $totalTax += $bracketTax;
            
            $bracketDetails[] = [
                'bracket_name' => $bracket->bracket_name,
                'min_amount' => $bracketMin,
                'max_amount' => $bracket->max_amount,
                'rate' => $bracket->rate,
                'taxable_amount' => $taxableInBracket,
                'tax_amount' => $bracketTax,
            ];
        }

        return [
            'tax_amount' => $totalTax,
            'bracket_details' => $bracketDetails,
        ];
    }

    /**
     * Apply tax exemptions.
     */
    protected function applyExemptions($exemptionIds, $taxAmount, $baseAmount, $context = [])
    {
        if (!$exemptionIds || !is_array($exemptionIds)) {
            return ['total_exemption' => 0, 'applied_exemptions' => []];
        }

        $exemptions = TaxExemption::whereIn('id', $exemptionIds)
                                 ->validOn()
                                 ->get();

        $totalExemption = 0;
        $appliedExemptions = [];

        foreach ($exemptions as $exemption) {
            $exemptionAmount = $exemption->calculateExemption($taxAmount, $baseAmount, $context);
            
            if ($exemptionAmount > 0) {
                $totalExemption += $exemptionAmount;
                $appliedExemptions[] = [
                    'exemption_id' => $exemption->id,
                    'exemption_name' => $exemption->name,
                    'exemption_type' => $exemption->exemption_type,
                    'exemption_amount' => $exemptionAmount,
                ];
            }
        }

        return [
            'total_exemption' => min($totalExemption, $taxAmount), // Can't exempt more than tax
            'applied_exemptions' => $appliedExemptions,
        ];
    }

    /**
     * Apply tax rules.
     */
    protected function applyTaxRules($item, $baseAmount, $taxAmount, $context = [])
    {
        $rules = TaxRule::where('is_active', true)
                       ->where(function ($query) {
                           $query->whereNull('effective_from')
                                 ->orWhere('effective_from', '<=', now());
                       })
                       ->where(function ($query) {
                           $query->whereNull('effective_to')
                                 ->orWhere('effective_to', '>=', now());
                       })
                       ->orderBy('priority')
                       ->get();

        $adjustedTax = $taxAmount;
        $appliedRules = [];

        foreach ($rules as $rule) {
            if ($this->isRuleApplicable($rule, $item, $baseAmount, $context)) {
                $ruleResult = $this->applyRule($rule, $baseAmount, $adjustedTax, $context);
                $adjustedTax = $ruleResult['adjusted_tax'];
                
                if ($ruleResult['applied']) {
                    $appliedRules[] = [
                        'rule_id' => $rule->id,
                        'rule_name' => $rule->name,
                        'rule_type' => $rule->rule_type,
                        'adjustment' => $ruleResult['adjustment'],
                    ];
                }
            }
        }

        return [
            'adjusted_tax' => $adjustedTax,
            'applied_rules' => $appliedRules,
        ];
    }

    /**
     * Check if a tax rule is applicable.
     */
    protected function isRuleApplicable($rule, $item, $amount, $context = [])
    {
        $conditions = $rule->conditions;
        
        if (!$conditions) {
            return true;
        }

        // Check entity applicability
        if ($rule->applicable_entities) {
            $entities = $rule->applicable_entities;
            
            if (isset($entities['items']) && !in_array($item->id, $entities['items'])) {
                return false;
            }
            
            if (isset($entities['categories']) && !in_array($item->category_id, $entities['categories'])) {
                return false;
            }
        }

        // Check other conditions
        foreach ($conditions as $condition) {
            if (!$this->checkRuleCondition($condition, $amount, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Apply a specific tax rule.
     */
    protected function applyRule($rule, $baseAmount, $currentTax, $context = [])
    {
        $actions = $rule->actions;
        $adjustedTax = $currentTax;
        $applied = false;

        foreach ($actions as $action) {
            switch ($action['type']) {
                case 'multiply_rate':
                    $adjustedTax *= $action['value'];
                    $applied = true;
                    break;
                    
                case 'add_amount':
                    $adjustedTax += $action['value'];
                    $applied = true;
                    break;
                    
                case 'set_minimum':
                    $adjustedTax = max($adjustedTax, $action['value']);
                    $applied = true;
                    break;
                    
                case 'set_maximum':
                    $adjustedTax = min($adjustedTax, $action['value']);
                    $applied = true;
                    break;
                    
                case 'reverse_charge':
                    $adjustedTax = 0; // Customer pays, but recorded differently
                    $context['reverse_charge'] = $currentTax;
                    $applied = true;
                    break;
            }
        }

        return [
            'adjusted_tax' => $adjustedTax,
            'adjustment' => $adjustedTax - $currentTax,
            'applied' => $applied,
        ];
    }

    /**
     * Store tax calculation record.
     */
    protected function storeTaxCalculation($entity, $type, $baseAmount, $taxAmount, $details, $context = [])
    {
        // This would store detailed calculation records for audit purposes
        // Implementation depends on specific requirements
    }

    /**
     * Create standardized tax result.
     */
    protected function createTaxResult($taxAmount, $exemptionAmount, $netAmount, $calculations)
    {
        return [
            'net_amount' => round($netAmount, 4),
            'tax_amount' => round($taxAmount, 4),
            'exemption_amount' => round($exemptionAmount, 4),
            'total_amount' => round($netAmount + $taxAmount, 4),
            'effective_tax_rate' => $netAmount > 0 ? round(($taxAmount / $netAmount) * 100, 4) : 0,
            'calculations' => $calculations,
        ];
    }

    /**
     * Check rule condition.
     */
    protected function checkRuleCondition($condition, $amount, $context = [])
    {
        // Implementation for checking various rule conditions
        // This would be expanded based on specific business rules
        return true;
    }
}

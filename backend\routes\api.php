<?php

use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// مسارات المصادقة (غير محمية)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// مسارات محمية بالمصادقة
Route::middleware('auth:sanctum')->group(function () {
    // مسارات المصادقة المحمية
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/logout-all', [AuthController::class, 'logoutAll']);
        Route::get('/user', [AuthController::class, 'user']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
    });

    // مسار للتحقق من حالة المصادقة
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // مسارات الضرائب
    Route::prefix('tax-types')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxTypeController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxTypeController::class, 'getActiveTaxTypes']);
        Route::get('/statistics', [App\Http\Controllers\TaxTypeController::class, 'getStatistics']);
        Route::post('/', [App\Http\Controllers\TaxTypeController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxTypeController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxTypeController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxTypeController::class, 'destroy'])->middleware('permission:settings.edit');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\TaxTypeController::class, 'toggleStatus'])->middleware('permission:settings.edit');
        Route::post('/calculate', [App\Http\Controllers\TaxTypeController::class, 'calculateTax']);
        Route::post('/calculate-reverse', [App\Http\Controllers\TaxTypeController::class, 'calculateReverseTax']);
    });
});

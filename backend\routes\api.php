<?php

use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// مسارات المصادقة (غير محمية)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// مسارات محمية بالمصادقة
Route::middleware('auth:sanctum')->group(function () {
    // مسارات المصادقة المحمية
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/logout-all', [AuthController::class, 'logoutAll']);
        Route::get('/user', [AuthController::class, 'user']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
    });

    // مسار للتحقق من حالة المصادقة
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // مسارات الضرائب
    Route::prefix('tax-types')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxTypeController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxTypeController::class, 'getActiveTaxTypes']);
        Route::get('/statistics', [App\Http\Controllers\TaxTypeController::class, 'getStatistics']);
        Route::post('/', [App\Http\Controllers\TaxTypeController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxTypeController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxTypeController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxTypeController::class, 'destroy'])->middleware('permission:settings.edit');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\TaxTypeController::class, 'toggleStatus'])->middleware('permission:settings.edit');
        Route::post('/calculate', [App\Http\Controllers\TaxTypeController::class, 'calculateTax']);
        Route::post('/calculate-reverse', [App\Http\Controllers\TaxTypeController::class, 'calculateReverseTax']);
    });

    // مسارات التقارير الضريبية
    Route::prefix('tax-reports')->middleware('permission:reports.financial')->group(function () {
        Route::get('/vat', [App\Http\Controllers\TaxReportController::class, 'vatReport']);
        Route::get('/excise', [App\Http\Controllers\TaxReportController::class, 'exciseTaxReport']);
        Route::get('/exemptions', [App\Http\Controllers\TaxReportController::class, 'taxExemptionReport']);
        Route::post('/returns', [App\Http\Controllers\TaxReportController::class, 'createTaxReturn']);
    });

    // مسارات المجموعات الضريبية
    Route::prefix('tax-groups')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxGroupController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxGroupController::class, 'getActiveGroups']);
        Route::post('/', [App\Http\Controllers\TaxGroupController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxGroupController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxGroupController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxGroupController::class, 'destroy'])->middleware('permission:settings.edit');
    });

    // مسارات الإعفاءات الضريبية
    Route::prefix('tax-exemptions')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxExemptionController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxExemptionController::class, 'getActiveExemptions']);
        Route::post('/', [App\Http\Controllers\TaxExemptionController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'destroy'])->middleware('permission:settings.edit');
    });

    // مسارات دليل الحسابات
    Route::prefix('accounts')->group(function () {
        Route::get('/', [App\Http\Controllers\AccountController::class, 'index'])->middleware('permission:accounts.view');
        Route::get('/active', [App\Http\Controllers\AccountController::class, 'getActiveAccounts'])->middleware('permission:accounts.view');
        Route::post('/', [App\Http\Controllers\AccountController::class, 'store'])->middleware('permission:accounts.create');
        Route::get('/{id}', [App\Http\Controllers\AccountController::class, 'show'])->middleware('permission:accounts.view');
        Route::put('/{id}', [App\Http\Controllers\AccountController::class, 'update'])->middleware('permission:accounts.edit');
        Route::delete('/{id}', [App\Http\Controllers\AccountController::class, 'destroy'])->middleware('permission:accounts.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\AccountController::class, 'toggleStatus'])->middleware('permission:accounts.edit');
        Route::get('/{id}/statement', [App\Http\Controllers\AccountController::class, 'statement'])->middleware('permission:accounts.view');
    });

    // مسارات القيود المحاسبية
    Route::prefix('journal-entries')->group(function () {
        Route::get('/', [App\Http\Controllers\JournalEntryController::class, 'index'])->middleware('permission:accounting.view');
        Route::get('/statistics', [App\Http\Controllers\JournalEntryController::class, 'getStatistics'])->middleware('permission:accounting.view');
        Route::post('/', [App\Http\Controllers\JournalEntryController::class, 'store'])->middleware('permission:accounting.create');
        Route::get('/{id}', [App\Http\Controllers\JournalEntryController::class, 'show'])->middleware('permission:accounting.view');
        Route::put('/{id}', [App\Http\Controllers\JournalEntryController::class, 'update'])->middleware('permission:accounting.edit');
        Route::delete('/{id}', [App\Http\Controllers\JournalEntryController::class, 'destroy'])->middleware('permission:accounting.delete');
        Route::post('/{id}/approve', [App\Http\Controllers\JournalEntryController::class, 'approve'])->middleware('permission:accounting.approve');
        Route::post('/{id}/reverse', [App\Http\Controllers\JournalEntryController::class, 'reverse'])->middleware('permission:accounting.approve');
    });

    // مسارات مراكز التكلفة
    Route::prefix('cost-centers')->group(function () {
        Route::get('/', [App\Http\Controllers\CostCenterController::class, 'index'])->middleware('permission:accounts.view');
        Route::get('/active', [App\Http\Controllers\CostCenterController::class, 'getActiveCostCenters'])->middleware('permission:accounts.view');
        Route::post('/', [App\Http\Controllers\CostCenterController::class, 'store'])->middleware('permission:accounts.create');
        Route::get('/{id}', [App\Http\Controllers\CostCenterController::class, 'show'])->middleware('permission:accounts.view');
        Route::put('/{id}', [App\Http\Controllers\CostCenterController::class, 'update'])->middleware('permission:accounts.edit');
        Route::delete('/{id}', [App\Http\Controllers\CostCenterController::class, 'destroy'])->middleware('permission:accounts.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\CostCenterController::class, 'toggleStatus'])->middleware('permission:accounts.edit');
        Route::get('/{id}/report', [App\Http\Controllers\CostCenterController::class, 'report'])->middleware('permission:accounts.view');
    });

    // مسارات التقارير المالية
    Route::prefix('financial-reports')->middleware('permission:reports.financial')->group(function () {
        Route::get('/income-statement', [App\Http\Controllers\FinancialReportController::class, 'incomeStatement']);
        Route::get('/balance-sheet', [App\Http\Controllers\FinancialReportController::class, 'balanceSheet']);
        Route::get('/cash-flow', [App\Http\Controllers\FinancialReportController::class, 'cashFlowStatement']);
        Route::get('/detailed-profit-loss', [App\Http\Controllers\FinancialReportController::class, 'detailedProfitLoss']);
        Route::get('/comparative', [App\Http\Controllers\FinancialReportController::class, 'comparativeReport']);
        Route::get('/accounts', [App\Http\Controllers\FinancialReportController::class, 'accountsReport']);
        Route::get('/financial-ratios', [App\Http\Controllers\FinancialReportController::class, 'financialRatios']);
    });
});

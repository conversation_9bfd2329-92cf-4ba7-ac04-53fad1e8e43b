<?php

use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// مسارات المصادقة (غير محمية)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// مسارات محمية بالمصادقة
Route::middleware('auth:sanctum')->group(function () {
    // مسارات المصادقة المحمية
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/logout-all', [AuthController::class, 'logoutAll']);
        Route::get('/user', [AuthController::class, 'user']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
    });

    // مسار للتحقق من حالة المصادقة
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // مسارات الضرائب
    Route::prefix('tax-types')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxTypeController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxTypeController::class, 'getActiveTaxTypes']);
        Route::get('/statistics', [App\Http\Controllers\TaxTypeController::class, 'getStatistics']);
        Route::post('/', [App\Http\Controllers\TaxTypeController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxTypeController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxTypeController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxTypeController::class, 'destroy'])->middleware('permission:settings.edit');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\TaxTypeController::class, 'toggleStatus'])->middleware('permission:settings.edit');
        Route::post('/calculate', [App\Http\Controllers\TaxTypeController::class, 'calculateTax']);
        Route::post('/calculate-reverse', [App\Http\Controllers\TaxTypeController::class, 'calculateReverseTax']);
    });

    // مسارات التقارير الضريبية
    Route::prefix('tax-reports')->middleware('permission:reports.financial')->group(function () {
        Route::get('/vat', [App\Http\Controllers\TaxReportController::class, 'vatReport']);
        Route::get('/excise', [App\Http\Controllers\TaxReportController::class, 'exciseTaxReport']);
        Route::get('/exemptions', [App\Http\Controllers\TaxReportController::class, 'taxExemptionReport']);
        Route::post('/returns', [App\Http\Controllers\TaxReportController::class, 'createTaxReturn']);
    });

    // مسارات المجموعات الضريبية
    Route::prefix('tax-groups')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxGroupController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxGroupController::class, 'getActiveGroups']);
        Route::post('/', [App\Http\Controllers\TaxGroupController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxGroupController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxGroupController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxGroupController::class, 'destroy'])->middleware('permission:settings.edit');
    });

    // مسارات الإعفاءات الضريبية
    Route::prefix('tax-exemptions')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxExemptionController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxExemptionController::class, 'getActiveExemptions']);
        Route::post('/', [App\Http\Controllers\TaxExemptionController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'destroy'])->middleware('permission:settings.edit');
    });

    // مسارات دليل الحسابات
    Route::prefix('accounts')->group(function () {
        Route::get('/', [App\Http\Controllers\AccountController::class, 'index'])->middleware('permission:accounts.view');
        Route::get('/active', [App\Http\Controllers\AccountController::class, 'getActiveAccounts'])->middleware('permission:accounts.view');
        Route::post('/', [App\Http\Controllers\AccountController::class, 'store'])->middleware('permission:accounts.create');
        Route::get('/{id}', [App\Http\Controllers\AccountController::class, 'show'])->middleware('permission:accounts.view');
        Route::put('/{id}', [App\Http\Controllers\AccountController::class, 'update'])->middleware('permission:accounts.edit');
        Route::delete('/{id}', [App\Http\Controllers\AccountController::class, 'destroy'])->middleware('permission:accounts.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\AccountController::class, 'toggleStatus'])->middleware('permission:accounts.edit');
        Route::get('/{id}/statement', [App\Http\Controllers\AccountController::class, 'statement'])->middleware('permission:accounts.view');
    });

    // مسارات القيود المحاسبية
    Route::prefix('journal-entries')->group(function () {
        Route::get('/', [App\Http\Controllers\JournalEntryController::class, 'index'])->middleware('permission:accounting.view');
        Route::get('/statistics', [App\Http\Controllers\JournalEntryController::class, 'getStatistics'])->middleware('permission:accounting.view');
        Route::post('/', [App\Http\Controllers\JournalEntryController::class, 'store'])->middleware('permission:accounting.create');
        Route::get('/{id}', [App\Http\Controllers\JournalEntryController::class, 'show'])->middleware('permission:accounting.view');
        Route::put('/{id}', [App\Http\Controllers\JournalEntryController::class, 'update'])->middleware('permission:accounting.edit');
        Route::delete('/{id}', [App\Http\Controllers\JournalEntryController::class, 'destroy'])->middleware('permission:accounting.delete');
        Route::post('/{id}/approve', [App\Http\Controllers\JournalEntryController::class, 'approve'])->middleware('permission:accounting.approve');
        Route::post('/{id}/reverse', [App\Http\Controllers\JournalEntryController::class, 'reverse'])->middleware('permission:accounting.approve');
    });

    // مسارات مراكز التكلفة
    Route::prefix('cost-centers')->group(function () {
        Route::get('/', [App\Http\Controllers\CostCenterController::class, 'index'])->middleware('permission:accounts.view');
        Route::get('/active', [App\Http\Controllers\CostCenterController::class, 'getActiveCostCenters'])->middleware('permission:accounts.view');
        Route::post('/', [App\Http\Controllers\CostCenterController::class, 'store'])->middleware('permission:accounts.create');
        Route::get('/{id}', [App\Http\Controllers\CostCenterController::class, 'show'])->middleware('permission:accounts.view');
        Route::put('/{id}', [App\Http\Controllers\CostCenterController::class, 'update'])->middleware('permission:accounts.edit');
        Route::delete('/{id}', [App\Http\Controllers\CostCenterController::class, 'destroy'])->middleware('permission:accounts.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\CostCenterController::class, 'toggleStatus'])->middleware('permission:accounts.edit');
        Route::get('/{id}/report', [App\Http\Controllers\CostCenterController::class, 'report'])->middleware('permission:accounts.view');
    });

    // مسارات التقارير المالية
    Route::prefix('financial-reports')->middleware('permission:reports.financial')->group(function () {
        Route::get('/income-statement', [App\Http\Controllers\FinancialReportController::class, 'incomeStatement']);
        Route::get('/balance-sheet', [App\Http\Controllers\FinancialReportController::class, 'balanceSheet']);
        Route::get('/cash-flow', [App\Http\Controllers\FinancialReportController::class, 'cashFlowStatement']);
        Route::get('/detailed-profit-loss', [App\Http\Controllers\FinancialReportController::class, 'detailedProfitLoss']);
        Route::get('/comparative', [App\Http\Controllers\FinancialReportController::class, 'comparativeReport']);
        Route::get('/accounts', [App\Http\Controllers\FinancialReportController::class, 'accountsReport']);
        Route::get('/financial-ratios', [App\Http\Controllers\FinancialReportController::class, 'financialRatios']);
    });

    // مسارات العملاء
    Route::prefix('customers')->group(function () {
        Route::get('/', [App\Http\Controllers\CustomerController::class, 'index'])->middleware('permission:customers.view');
        Route::get('/active', [App\Http\Controllers\CustomerController::class, 'getActiveCustomers'])->middleware('permission:customers.view');
        Route::get('/statistics', [App\Http\Controllers\CustomerController::class, 'getStatistics'])->middleware('permission:customers.view');
        Route::get('/exceeded-credit-limit', [App\Http\Controllers\CustomerController::class, 'exceededCreditLimitReport'])->middleware('permission:customers.view');
        Route::post('/', [App\Http\Controllers\CustomerController::class, 'store'])->middleware('permission:customers.create');
        Route::get('/{id}', [App\Http\Controllers\CustomerController::class, 'show'])->middleware('permission:customers.view');
        Route::put('/{id}', [App\Http\Controllers\CustomerController::class, 'update'])->middleware('permission:customers.edit');
        Route::delete('/{id}', [App\Http\Controllers\CustomerController::class, 'destroy'])->middleware('permission:customers.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\CustomerController::class, 'toggleStatus'])->middleware('permission:customers.edit');
        Route::get('/{id}/statement', [App\Http\Controllers\CustomerController::class, 'statement'])->middleware('permission:customers.view');
        Route::get('/{id}/aging', [App\Http\Controllers\CustomerController::class, 'agingAnalysis'])->middleware('permission:customers.view');
    });

    // مسارات الموردين
    Route::prefix('suppliers')->group(function () {
        Route::get('/', [App\Http\Controllers\SupplierController::class, 'index'])->middleware('permission:suppliers.view');
        Route::get('/active', [App\Http\Controllers\SupplierController::class, 'getActiveSuppliers'])->middleware('permission:suppliers.view');
        Route::get('/statistics', [App\Http\Controllers\SupplierController::class, 'getStatistics'])->middleware('permission:suppliers.view');
        Route::get('/top-suppliers', [App\Http\Controllers\SupplierController::class, 'topSuppliersReport'])->middleware('permission:suppliers.view');
        Route::post('/', [App\Http\Controllers\SupplierController::class, 'store'])->middleware('permission:suppliers.create');
        Route::get('/{id}', [App\Http\Controllers\SupplierController::class, 'show'])->middleware('permission:suppliers.view');
        Route::put('/{id}', [App\Http\Controllers\SupplierController::class, 'update'])->middleware('permission:suppliers.edit');
        Route::delete('/{id}', [App\Http\Controllers\SupplierController::class, 'destroy'])->middleware('permission:suppliers.delete');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\SupplierController::class, 'toggleStatus'])->middleware('permission:suppliers.edit');
        Route::get('/{id}/statement', [App\Http\Controllers\SupplierController::class, 'statement'])->middleware('permission:suppliers.view');
        Route::get('/{id}/aging', [App\Http\Controllers\SupplierController::class, 'agingAnalysis'])->middleware('permission:suppliers.view');
    });

    // مسارات المخزون
    Route::prefix('inventory')->group(function () {
        Route::get('/current-stock', [App\Http\Controllers\InventoryController::class, 'currentStock'])->middleware('permission:inventory.view');
        Route::get('/stock-movements', [App\Http\Controllers\InventoryController::class, 'stockMovements'])->middleware('permission:inventory.view');
        Route::get('/low-stock-report', [App\Http\Controllers\InventoryController::class, 'lowStockReport'])->middleware('permission:inventory.view');
        Route::get('/valuation-report', [App\Http\Controllers\InventoryController::class, 'stockValuationReport'])->middleware('permission:inventory.view');
        Route::get('/statistics', [App\Http\Controllers\InventoryController::class, 'getStatistics'])->middleware('permission:inventory.view');
        Route::post('/adjust-stock', [App\Http\Controllers\InventoryController::class, 'adjustStock'])->middleware('permission:inventory.edit');
        Route::post('/transfer-stock', [App\Http\Controllers\InventoryController::class, 'transferStock'])->middleware('permission:inventory.edit');
    });
});

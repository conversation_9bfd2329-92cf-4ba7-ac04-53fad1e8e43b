<?php

use App\Http\Controllers\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// مسارات المصادقة (غير محمية)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// مسارات محمية بالمصادقة
Route::middleware('auth:sanctum')->group(function () {
    // مسارات المصادقة المحمية
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::post('/logout-all', [AuthController::class, 'logoutAll']);
        Route::get('/user', [AuthController::class, 'user']);
        Route::post('/change-password', [AuthController::class, 'changePassword']);
        Route::put('/profile', [AuthController::class, 'updateProfile']);
        Route::post('/refresh-token', [AuthController::class, 'refreshToken']);
    });

    // مسار للتحقق من حالة المصادقة
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // مسارات الضرائب
    Route::prefix('tax-types')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxTypeController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxTypeController::class, 'getActiveTaxTypes']);
        Route::get('/statistics', [App\Http\Controllers\TaxTypeController::class, 'getStatistics']);
        Route::post('/', [App\Http\Controllers\TaxTypeController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxTypeController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxTypeController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxTypeController::class, 'destroy'])->middleware('permission:settings.edit');
        Route::patch('/{id}/toggle-status', [App\Http\Controllers\TaxTypeController::class, 'toggleStatus'])->middleware('permission:settings.edit');
        Route::post('/calculate', [App\Http\Controllers\TaxTypeController::class, 'calculateTax']);
        Route::post('/calculate-reverse', [App\Http\Controllers\TaxTypeController::class, 'calculateReverseTax']);
    });

    // مسارات التقارير الضريبية
    Route::prefix('tax-reports')->middleware('permission:reports.financial')->group(function () {
        Route::get('/vat', [App\Http\Controllers\TaxReportController::class, 'vatReport']);
        Route::get('/excise', [App\Http\Controllers\TaxReportController::class, 'exciseTaxReport']);
        Route::get('/exemptions', [App\Http\Controllers\TaxReportController::class, 'taxExemptionReport']);
        Route::post('/returns', [App\Http\Controllers\TaxReportController::class, 'createTaxReturn']);
    });

    // مسارات المجموعات الضريبية
    Route::prefix('tax-groups')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxGroupController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxGroupController::class, 'getActiveGroups']);
        Route::post('/', [App\Http\Controllers\TaxGroupController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxGroupController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxGroupController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxGroupController::class, 'destroy'])->middleware('permission:settings.edit');
    });

    // مسارات الإعفاءات الضريبية
    Route::prefix('tax-exemptions')->group(function () {
        Route::get('/', [App\Http\Controllers\TaxExemptionController::class, 'index']);
        Route::get('/active', [App\Http\Controllers\TaxExemptionController::class, 'getActiveExemptions']);
        Route::post('/', [App\Http\Controllers\TaxExemptionController::class, 'store'])->middleware('permission:settings.edit');
        Route::get('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'show']);
        Route::put('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'update'])->middleware('permission:settings.edit');
        Route::delete('/{id}', [App\Http\Controllers\TaxExemptionController::class, 'destroy'])->middleware('permission:settings.edit');
    });
});

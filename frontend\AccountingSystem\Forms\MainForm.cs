using AccountingSystem.Services;
using AccountingSystem.Models;
using AccountingSystem.Forms.Accounting;
using AccountingSystem.Forms.Sales;
using AccountingSystem.Forms.Reports;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingSystem.Forms
{
    public partial class MainForm : Form
    {
        private readonly IAuthService _authService;
        private readonly IServiceProvider _serviceProvider;
        private MenuStrip? _menuStrip;
        private StatusStrip? _statusStrip;
        private ToolStripStatusLabel? _userLabel;
        private ToolStripStatusLabel? _timeLabel;
        private Timer? _timeTimer;

        public MainForm(IAuthService authService, IServiceProvider serviceProvider)
        {
            _authService = authService;
            _serviceProvider = serviceProvider;
            InitializeComponent();
            SetupForm();
            SetupMenu();
            SetupStatusBar();
            SetupEvents();
        }

        private void SetupForm()
        {
            this.Text = "نظام المحاسبة المتكامل";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.IsMdiContainer = true;
            this.BackColor = Color.FromArgb(240, 240, 240);
        }

        private void SetupMenu()
        {
            _menuStrip = new MenuStrip
            {
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White
            };

            // File Menu
            var fileMenu = new ToolStripMenuItem("ملف");
            fileMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("إعدادات", null, (s, e) => OpenSettingsForm()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تسجيل الخروج", null, (s, e) => LogoutAsync()),
                new ToolStripMenuItem("خروج", null, (s, e) => this.Close())
            });

            // Accounting Menu
            var accountingMenu = new ToolStripMenuItem("المحاسبة");
            accountingMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("دليل الحسابات", null, (s, e) => OpenAccountsForm()),
                new ToolStripMenuItem("القيود المحاسبية", null, (s, e) => OpenJournalEntriesForm()),
                new ToolStripMenuItem("مراكز التكلفة", null, (s, e) => OpenCostCentersForm()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("ميزان المراجعة", null, (s, e) => OpenTrialBalanceForm())
            });

            // Sales Menu
            var salesMenu = new ToolStripMenuItem("المبيعات");
            salesMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("العملاء", null, (s, e) => OpenCustomersForm()),
                new ToolStripMenuItem("فواتير المبيعات", null, (s, e) => OpenSalesInvoicesForm()),
                new ToolStripMenuItem("مردودات المبيعات", null, (s, e) => OpenSalesReturnsForm()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تقارير المبيعات", null, (s, e) => OpenSalesReportsForm())
            });

            // Purchases Menu
            var purchasesMenu = new ToolStripMenuItem("المشتريات");
            purchasesMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("الموردين", null, (s, e) => OpenSuppliersForm()),
                new ToolStripMenuItem("فواتير المشتريات", null, (s, e) => OpenPurchaseInvoicesForm()),
                new ToolStripMenuItem("مردودات المشتريات", null, (s, e) => OpenPurchaseReturnsForm()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تقارير المشتريات", null, (s, e) => OpenPurchaseReportsForm())
            });

            // Inventory Menu
            var inventoryMenu = new ToolStripMenuItem("المخزون");
            inventoryMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("الأصناف", null, (s, e) => OpenItemsForm()),
                new ToolStripMenuItem("المخازن", null, (s, e) => OpenWarehousesForm()),
                new ToolStripMenuItem("حركات المخزون", null, (s, e) => OpenStockMovementsForm()),
                new ToolStripSeparator(),
                new ToolStripMenuItem("تقارير المخزون", null, (s, e) => OpenInventoryReportsForm())
            });

            // Reports Menu
            var reportsMenu = new ToolStripMenuItem("التقارير");
            reportsMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("التقارير المالية", null, (s, e) => OpenFinancialReportsForm()),
                new ToolStripMenuItem("التقارير الضريبية", null, (s, e) => OpenTaxReportsForm()),
                new ToolStripMenuItem("تقارير العملاء", null, (s, e) => OpenCustomerReportsForm()),
                new ToolStripMenuItem("تقارير الموردين", null, (s, e) => OpenSupplierReportsForm())
            });

            // Help Menu
            var helpMenu = new ToolStripMenuItem("مساعدة");
            helpMenu.DropDownItems.AddRange(new ToolStripItem[]
            {
                new ToolStripMenuItem("دليل المستخدم", null, (s, e) => OpenUserGuide()),
                new ToolStripMenuItem("حول البرنامج", null, (s, e) => ShowAboutDialog())
            });

            _menuStrip.Items.AddRange(new ToolStripItem[]
            {
                fileMenu,
                accountingMenu,
                salesMenu,
                purchasesMenu,
                inventoryMenu,
                reportsMenu,
                helpMenu
            });

            this.MainMenuStrip = _menuStrip;
            this.Controls.Add(_menuStrip);
        }

        private void SetupStatusBar()
        {
            _statusStrip = new StatusStrip
            {
                BackColor = Color.FromArgb(240, 240, 240)
            };

            _userLabel = new ToolStripStatusLabel
            {
                Text = $"المستخدم: {_authService.CurrentUser?.Name ?? "غير محدد"}",
                Spring = true,
                TextAlign = ContentAlignment.MiddleLeft
            };

            _timeLabel = new ToolStripStatusLabel
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"),
                TextAlign = ContentAlignment.MiddleRight
            };

            _statusStrip.Items.AddRange(new ToolStripItem[]
            {
                _userLabel,
                _timeLabel
            });

            this.Controls.Add(_statusStrip);

            // Setup timer for time updates
            _timeTimer = new Timer
            {
                Interval = 1000,
                Enabled = true
            };
            _timeTimer.Tick += (s, e) =>
            {
                if (_timeLabel != null)
                    _timeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            };
        }

        private void SetupEvents()
        {
            _authService.UserChanged += AuthService_UserChanged;
            this.FormClosing += MainForm_FormClosing;
        }

        private void AuthService_UserChanged(object? sender, User? user)
        {
            if (_userLabel != null)
            {
                _userLabel.Text = $"المستخدم: {user?.Name ?? "غير محدد"}";
            }
        }

        private async void MainForm_FormClosing(object? sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            // Cleanup
            _timeTimer?.Stop();
            _timeTimer?.Dispose();

            // Logout if authenticated
            if (_authService.IsAuthenticated)
            {
                await _authService.LogoutAsync();
            }
        }

        // Menu event handlers
        private void OpenSettingsForm() => ShowMessage("إعدادات");

        private void OpenAccountsForm()
        {
            var accountsForm = new AccountsForm(_serviceProvider.GetRequiredService<IApiService>(),
                                              _serviceProvider.GetRequiredService<IAuthService>())
            {
                MdiParent = this
            };
            accountsForm.Show();
        }

        private void OpenJournalEntriesForm()
        {
            var journalEntriesForm = new JournalEntriesForm(_serviceProvider.GetRequiredService<IApiService>(),
                                                           _serviceProvider.GetRequiredService<IAuthService>())
            {
                MdiParent = this
            };
            journalEntriesForm.Show();
        }

        private void OpenCostCentersForm() => ShowMessage("مراكز التكلفة");
        private void OpenTrialBalanceForm() => ShowMessage("ميزان المراجعة");

        private void OpenCustomersForm()
        {
            var customersForm = new CustomersForm(_serviceProvider.GetRequiredService<IApiService>(),
                                                 _serviceProvider.GetRequiredService<IAuthService>())
            {
                MdiParent = this
            };
            customersForm.Show();
        }
        private void OpenSalesInvoicesForm() => ShowMessage("فواتير المبيعات");
        private void OpenSalesReturnsForm() => ShowMessage("مردودات المبيعات");
        private void OpenSalesReportsForm() => ShowMessage("تقارير المبيعات");
        private void OpenSuppliersForm() => ShowMessage("الموردين");
        private void OpenPurchaseInvoicesForm() => ShowMessage("فواتير المشتريات");
        private void OpenPurchaseReturnsForm() => ShowMessage("مردودات المشتريات");
        private void OpenPurchaseReportsForm() => ShowMessage("تقارير المشتريات");
        private void OpenItemsForm() => ShowMessage("الأصناف");
        private void OpenWarehousesForm() => ShowMessage("المخازن");
        private void OpenStockMovementsForm() => ShowMessage("حركات المخزون");
        private void OpenInventoryReportsForm() => ShowMessage("تقارير المخزون");

        private void OpenFinancialReportsForm()
        {
            var financialReportsForm = new FinancialReportsForm(_serviceProvider.GetRequiredService<IApiService>(),
                                                               _serviceProvider.GetRequiredService<IAuthService>())
            {
                MdiParent = this
            };
            financialReportsForm.Show();
        }

        private void OpenTaxReportsForm() => ShowMessage("التقارير الضريبية");
        private void OpenCustomerReportsForm() => ShowMessage("تقارير العملاء");
        private void OpenSupplierReportsForm() => ShowMessage("تقارير الموردين");
        private void OpenUserGuide() => ShowMessage("دليل المستخدم");

        private void ShowAboutDialog()
        {
            MessageBox.Show(
                "نظام المحاسبة المتكامل\nالإصدار 1.0.0\n\n© 2024 شركة التقنيات المتقدمة\nجميع الحقوق محفوظة",
                "حول البرنامج",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private async void LogoutAsync()
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد تسجيل الخروج",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                await _authService.LogoutAsync();
                this.Hide();
                
                var loginForm = _serviceProvider.GetRequiredService<LoginForm>();
                if (loginForm.ShowDialog() == DialogResult.OK)
                {
                    this.Show();
                }
                else
                {
                    this.Close();
                }
            }
        }

        private void ShowMessage(string feature)
        {
            MessageBox.Show($"سيتم فتح نموذج {feature} قريباً", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Account extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'name_en',
        'parent_id',
        'account_type',
        'level',
        'is_active',
        'opening_balance',
        'current_balance',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'opening_balance' => 'decimal:2',
        'current_balance' => 'decimal:2',
        'level' => 'integer',
    ];

    /**
     * Get the parent account.
     */
    public function parent()
    {
        return $this->belongsTo(Account::class, 'parent_id');
    }

    /**
     * Get the child accounts.
     */
    public function children()
    {
        return $this->hasMany(Account::class, 'parent_id');
    }

    /**
     * Get all descendants (children, grandchildren, etc.)
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get the journal entry details for this account.
     */
    public function journalEntryDetails()
    {
        return $this->hasMany(JournalEntryDetail::class);
    }

    /**
     * Scope a query to only include active accounts.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include accounts of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('account_type', $type);
    }

    /**
     * Scope a query to only include parent accounts (level 1).
     */
    public function scopeParents($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope a query to only include leaf accounts (no children).
     */
    public function scopeLeaves($query)
    {
        return $query->whereDoesntHave('children');
    }

    /**
     * Update account balance.
     */
    public function updateBalance($amount, $type = 'debit')
    {
        if ($type === 'debit') {
            $this->increment('current_balance', $amount);
        } else {
            $this->decrement('current_balance', $amount);
        }
    }

    /**
     * Get account balance as of a specific date.
     */
    public function getBalanceAsOf($date)
    {
        $entries = $this->journalEntryDetails()
            ->whereHas('journalEntry', function ($query) use ($date) {
                $query->where('entry_date', '<=', $date)
                      ->where('status', 'approved');
            })
            ->get();

        $balance = $this->opening_balance;
        
        foreach ($entries as $entry) {
            if ($this->account_type === 'asset' || $this->account_type === 'expense') {
                $balance += $entry->debit - $entry->credit;
            } else {
                $balance += $entry->credit - $entry->debit;
            }
        }

        return $balance;
    }

    /**
     * Check if account can be deleted.
     */
    public function canBeDeleted()
    {
        return $this->children()->count() === 0 && 
               $this->journalEntryDetails()->count() === 0;
    }
}

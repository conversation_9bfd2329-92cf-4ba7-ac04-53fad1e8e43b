using AccountingSystem.Services;
using AccountingSystem.Models;

namespace AccountingSystem.Forms.Accounting
{
    public partial class AccountsForm : Form
    {
        private readonly IApiService _apiService;
        private readonly IAuthService _authService;
        private DataGridView? _accountsGrid;
        private TreeView? _accountsTree;
        private TextBox? _searchTextBox;
        private Button? _addButton;
        private Button? _editButton;
        private Button? _deleteButton;
        private Button? _refreshButton;
        private List<Account> _accounts = new();

        public AccountsForm(IApiService apiService, IAuthService authService)
        {
            _apiService = apiService;
            _authService = authService;
            InitializeComponent();
            SetupForm();
            LoadAccountsAsync();
        }

        private void SetupForm()
        {
            this.Text = "دليل الحسابات";
            this.Size = new Size(1000, 600);
            this.StartPosition = FormStartPosition.CenterParent;

            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Search panel
            var searchPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(240, 240, 240)
            };

            var searchLabel = new Label
            {
                Text = "البحث:",
                Location = new Point(850, 15),
                Size = new Size(50, 20)
            };

            _searchTextBox = new TextBox
            {
                Location = new Point(650, 12),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10)
            };

            var searchButton = new Button
            {
                Text = "بحث",
                Location = new Point(580, 10),
                Size = new Size(60, 30),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            searchPanel.Controls.AddRange(new Control[] { searchLabel, _searchTextBox, searchButton });

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 50,
                BackColor = Color.FromArgb(250, 250, 250)
            };

            _addButton = new Button
            {
                Text = "إضافة",
                Location = new Point(10, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            _editButton = new Button
            {
                Text = "تعديل",
                Location = new Point(100, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _deleteButton = new Button
            {
                Text = "حذف",
                Location = new Point(190, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };

            _refreshButton = new Button
            {
                Text = "تحديث",
                Location = new Point(280, 10),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            buttonsPanel.Controls.AddRange(new Control[] { _addButton, _editButton, _deleteButton, _refreshButton });

            // Split container for tree and grid
            var splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                SplitterDistance = 300,
                Orientation = Orientation.Vertical
            };

            // Accounts tree
            _accountsTree = new TreeView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                ShowLines = true,
                ShowPlusMinus = true,
                ShowRootLines = true,
                FullRowSelect = true,
                HideSelection = false
            };

            // Accounts grid
            _accountsGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            SetupGridColumns();

            splitContainer.Panel1.Controls.Add(_accountsTree);
            splitContainer.Panel2.Controls.Add(_accountsGrid);

            this.Controls.AddRange(new Control[] { splitContainer, buttonsPanel, searchPanel });
        }

        private void SetupGridColumns()
        {
            if (_accountsGrid == null) return;

            _accountsGrid.Columns.AddRange(new DataGridViewColumn[]
            {
                new DataGridViewTextBoxColumn
                {
                    Name = "Code",
                    HeaderText = "الكود",
                    DataPropertyName = "Code",
                    Width = 100
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "Name",
                    HeaderText = "اسم الحساب",
                    DataPropertyName = "Name",
                    Width = 250
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "AccountTypeDisplay",
                    HeaderText = "نوع الحساب",
                    DataPropertyName = "AccountTypeDisplay",
                    Width = 120
                },
                new DataGridViewTextBoxColumn
                {
                    Name = "CurrentBalance",
                    HeaderText = "الرصيد الحالي",
                    DataPropertyName = "CurrentBalance",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                },
                new DataGridViewCheckBoxColumn
                {
                    Name = "IsActive",
                    HeaderText = "نشط",
                    DataPropertyName = "IsActive",
                    Width = 60
                }
            });
        }

        private void SetupLayout()
        {
            // Set right-to-left layout
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void SetupEvents()
        {
            if (_addButton != null)
                _addButton.Click += AddButton_Click;

            if (_editButton != null)
                _editButton.Click += EditButton_Click;

            if (_deleteButton != null)
                _deleteButton.Click += DeleteButton_Click;

            if (_refreshButton != null)
                _refreshButton.Click += RefreshButton_Click;

            if (_searchTextBox != null)
                _searchTextBox.TextChanged += SearchTextBox_TextChanged;

            if (_accountsGrid != null)
                _accountsGrid.SelectionChanged += AccountsGrid_SelectionChanged;

            if (_accountsTree != null)
                _accountsTree.AfterSelect += AccountsTree_AfterSelect;
        }

        private async void LoadAccountsAsync()
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;

                var response = await _apiService.GetAsync<List<Account>>("accounts?tree_format=true");

                if (response.Success && response.Data != null)
                {
                    _accounts = response.Data;
                    PopulateAccountsTree();
                    PopulateAccountsGrid();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void PopulateAccountsTree()
        {
            if (_accountsTree == null) return;

            _accountsTree.Nodes.Clear();

            var rootAccounts = _accounts.Where(a => a.ParentId == null).ToList();

            foreach (var account in rootAccounts)
            {
                var node = CreateTreeNode(account);
                _accountsTree.Nodes.Add(node);
                AddChildNodes(node, account.Id);
            }

            _accountsTree.ExpandAll();
        }

        private TreeNode CreateTreeNode(Account account)
        {
            return new TreeNode(account.DisplayName)
            {
                Tag = account,
                ForeColor = account.IsActive ? Color.Black : Color.Gray
            };
        }

        private void AddChildNodes(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _accounts.Where(a => a.ParentId == parentAccountId).ToList();

            foreach (var account in childAccounts)
            {
                var node = CreateTreeNode(account);
                parentNode.Nodes.Add(node);
                AddChildNodes(node, account.Id);
            }
        }

        private void PopulateAccountsGrid()
        {
            if (_accountsGrid == null) return;

            _accountsGrid.DataSource = _accounts.ToList();
        }

        // Event handlers
        private void AddButton_Click(object? sender, EventArgs e)
        {
            // TODO: Open add account form
            MessageBox.Show("سيتم فتح نموذج إضافة حساب جديد", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            // TODO: Open edit account form
            MessageBox.Show("سيتم فتح نموذج تعديل الحساب", "قيد التطوير", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private async void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (_accountsGrid?.SelectedRows.Count > 0)
            {
                var account = _accountsGrid.SelectedRows[0].DataBoundItem as Account;
                if (account != null)
                {
                    var result = MessageBox.Show($"هل تريد حذف الحساب '{account.Name}'؟", 
                        "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // TODO: Implement delete
                        MessageBox.Show("سيتم تنفيذ عملية الحذف", "قيد التطوير", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        private async void RefreshButton_Click(object? sender, EventArgs e)
        {
            await LoadAccountsAsync();
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            // TODO: Implement search filtering
        }

        private void AccountsGrid_SelectionChanged(object? sender, EventArgs e)
        {
            var hasSelection = _accountsGrid?.SelectedRows.Count > 0;
            if (_editButton != null) _editButton.Enabled = hasSelection;
            if (_deleteButton != null) _deleteButton.Enabled = hasSelection;
        }

        private void AccountsTree_AfterSelect(object? sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is Account account)
            {
                // Highlight corresponding row in grid
                if (_accountsGrid != null)
                {
                    foreach (DataGridViewRow row in _accountsGrid.Rows)
                    {
                        if (row.DataBoundItem is Account rowAccount && rowAccount.Id == account.Id)
                        {
                            row.Selected = true;
                            _accountsGrid.FirstDisplayedScrollingRowIndex = row.Index;
                            break;
                        }
                    }
                }
            }
        }
    }
}

{"name": "illuminate/cookie", "description": "The Illuminate Cookie package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "ext-hash": "*", "illuminate/collections": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "symfony/http-foundation": "^6.0", "symfony/http-kernel": "^6.0"}, "autoload": {"psr-4": {"Illuminate\\Cookie\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
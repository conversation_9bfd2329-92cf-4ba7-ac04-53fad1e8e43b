<?php

namespace Database\Seeders;

use App\Models\Account;
use App\Models\CostCenter;
use Illuminate\Database\Seeder;

class AccountsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // إنشاء الحسابات الرئيسية
        $this->createMainAccounts();
        
        // إنشاء الحسابات الفرعية
        $this->createSubAccounts();
        
        // إنشاء مراكز التكلفة
        $this->createCostCenters();

        $this->command->info('تم إنشاء دليل الحسابات ومراكز التكلفة بنجاح!');
    }

    /**
     * إنشاء الحسابات الرئيسية
     */
    protected function createMainAccounts()
    {
        $mainAccounts = [
            // الأصول
            [
                'code' => '1000',
                'name' => 'الأصول',
                'name_en' => 'Assets',
                'account_type' => 'asset',
                'level' => 1,
            ],
            
            // الخصوم
            [
                'code' => '2000',
                'name' => 'الخصوم',
                'name_en' => 'Liabilities',
                'account_type' => 'liability',
                'level' => 1,
            ],
            
            // حقوق الملكية
            [
                'code' => '3000',
                'name' => 'حقوق الملكية',
                'name_en' => 'Equity',
                'account_type' => 'equity',
                'level' => 1,
            ],
            
            // الإيرادات
            [
                'code' => '4000',
                'name' => 'الإيرادات',
                'name_en' => 'Revenue',
                'account_type' => 'revenue',
                'level' => 1,
            ],
            
            // المصروفات
            [
                'code' => '5000',
                'name' => 'المصروفات',
                'name_en' => 'Expenses',
                'account_type' => 'expense',
                'level' => 1,
            ],
        ];

        foreach ($mainAccounts as $account) {
            Account::firstOrCreate(
                ['code' => $account['code']],
                $account
            );
        }
    }

    /**
     * إنشاء الحسابات الفرعية
     */
    protected function createSubAccounts()
    {
        $subAccounts = [
            // الأصول المتداولة
            [
                'code' => '1100',
                'name' => 'الأصول المتداولة',
                'name_en' => 'Current Assets',
                'parent_code' => '1000',
                'account_type' => 'asset',
                'level' => 2,
            ],
            [
                'code' => '1101',
                'name' => 'النقدية في الصندوق',
                'name_en' => 'Cash in Hand',
                'parent_code' => '1100',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1102',
                'name' => 'النقدية في البنك',
                'name_en' => 'Cash in Bank',
                'parent_code' => '1100',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1103',
                'name' => 'العملاء',
                'name_en' => 'Accounts Receivable',
                'parent_code' => '1100',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1104',
                'name' => 'المخزون',
                'name_en' => 'Inventory',
                'parent_code' => '1100',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1105',
                'name' => 'المصروفات المدفوعة مقدماً',
                'name_en' => 'Prepaid Expenses',
                'parent_code' => '1100',
                'account_type' => 'asset',
                'level' => 3,
            ],

            // الأصول الثابتة
            [
                'code' => '1200',
                'name' => 'الأصول الثابتة',
                'name_en' => 'Fixed Assets',
                'parent_code' => '1000',
                'account_type' => 'asset',
                'level' => 2,
            ],
            [
                'code' => '1201',
                'name' => 'الأراضي',
                'name_en' => 'Land',
                'parent_code' => '1200',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1202',
                'name' => 'المباني',
                'name_en' => 'Buildings',
                'parent_code' => '1200',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1203',
                'name' => 'الأجهزة والمعدات',
                'name_en' => 'Equipment',
                'parent_code' => '1200',
                'account_type' => 'asset',
                'level' => 3,
            ],
            [
                'code' => '1204',
                'name' => 'السيارات',
                'name_en' => 'Vehicles',
                'parent_code' => '1200',
                'account_type' => 'asset',
                'level' => 3,
            ],

            // الخصوم المتداولة
            [
                'code' => '2100',
                'name' => 'الخصوم المتداولة',
                'name_en' => 'Current Liabilities',
                'parent_code' => '2000',
                'account_type' => 'liability',
                'level' => 2,
            ],
            [
                'code' => '2101',
                'name' => 'الموردين',
                'name_en' => 'Accounts Payable',
                'parent_code' => '2100',
                'account_type' => 'liability',
                'level' => 3,
            ],
            [
                'code' => '2102',
                'name' => 'المصروفات المستحقة',
                'name_en' => 'Accrued Expenses',
                'parent_code' => '2100',
                'account_type' => 'liability',
                'level' => 3,
            ],
            [
                'code' => '2103',
                'name' => 'ضريبة القيمة المضافة',
                'name_en' => 'VAT Payable',
                'parent_code' => '2100',
                'account_type' => 'liability',
                'level' => 3,
            ],

            // رأس المال
            [
                'code' => '3100',
                'name' => 'رأس المال',
                'name_en' => 'Capital',
                'parent_code' => '3000',
                'account_type' => 'equity',
                'level' => 2,
            ],
            [
                'code' => '3200',
                'name' => 'الأرباح المحتجزة',
                'name_en' => 'Retained Earnings',
                'parent_code' => '3000',
                'account_type' => 'equity',
                'level' => 2,
            ],

            // الإيرادات
            [
                'code' => '4100',
                'name' => 'إيرادات المبيعات',
                'name_en' => 'Sales Revenue',
                'parent_code' => '4000',
                'account_type' => 'revenue',
                'level' => 2,
            ],
            [
                'code' => '4200',
                'name' => 'إيرادات أخرى',
                'name_en' => 'Other Revenue',
                'parent_code' => '4000',
                'account_type' => 'revenue',
                'level' => 2,
            ],

            // المصروفات
            [
                'code' => '5100',
                'name' => 'تكلفة البضاعة المباعة',
                'name_en' => 'Cost of Goods Sold',
                'parent_code' => '5000',
                'account_type' => 'expense',
                'level' => 2,
            ],
            [
                'code' => '5200',
                'name' => 'مصروفات التشغيل',
                'name_en' => 'Operating Expenses',
                'parent_code' => '5000',
                'account_type' => 'expense',
                'level' => 2,
            ],
            [
                'code' => '5201',
                'name' => 'رواتب وأجور',
                'name_en' => 'Salaries and Wages',
                'parent_code' => '5200',
                'account_type' => 'expense',
                'level' => 3,
            ],
            [
                'code' => '5202',
                'name' => 'إيجارات',
                'name_en' => 'Rent Expense',
                'parent_code' => '5200',
                'account_type' => 'expense',
                'level' => 3,
            ],
            [
                'code' => '5203',
                'name' => 'كهرباء وماء',
                'name_en' => 'Utilities',
                'parent_code' => '5200',
                'account_type' => 'expense',
                'level' => 3,
            ],
            [
                'code' => '5204',
                'name' => 'مصروفات إعلان وتسويق',
                'name_en' => 'Marketing Expenses',
                'parent_code' => '5200',
                'account_type' => 'expense',
                'level' => 3,
            ],
        ];

        foreach ($subAccounts as $accountData) {
            $parentAccount = Account::where('code', $accountData['parent_code'])->first();
            
            if ($parentAccount) {
                Account::firstOrCreate(
                    ['code' => $accountData['code']],
                    [
                        'name' => $accountData['name'],
                        'name_en' => $accountData['name_en'],
                        'parent_id' => $parentAccount->id,
                        'account_type' => $accountData['account_type'],
                        'level' => $accountData['level'],
                    ]
                );
            }
        }
    }

    /**
     * إنشاء مراكز التكلفة
     */
    protected function createCostCenters()
    {
        $costCenters = [
            [
                'code' => 'CC001',
                'name' => 'الإدارة العامة',
                'description' => 'مركز تكلفة الإدارة العامة والأنشطة الإدارية',
            ],
            [
                'code' => 'CC002',
                'name' => 'قسم المبيعات',
                'description' => 'مركز تكلفة أنشطة المبيعات والتسويق',
            ],
            [
                'code' => 'CC003',
                'name' => 'قسم المشتريات',
                'description' => 'مركز تكلفة أنشطة المشتريات والتوريد',
            ],
            [
                'code' => 'CC004',
                'name' => 'قسم المخازن',
                'description' => 'مركز تكلفة إدارة المخازن والمخزون',
            ],
            [
                'code' => 'CC005',
                'name' => 'قسم المحاسبة',
                'description' => 'مركز تكلفة الأنشطة المحاسبية والمالية',
            ],
            [
                'code' => 'CC006',
                'name' => 'قسم الموارد البشرية',
                'description' => 'مركز تكلفة أنشطة الموارد البشرية',
            ],
        ];

        foreach ($costCenters as $costCenter) {
            CostCenter::firstOrCreate(
                ['code' => $costCenter['code']],
                $costCenter
            );
        }
    }
}

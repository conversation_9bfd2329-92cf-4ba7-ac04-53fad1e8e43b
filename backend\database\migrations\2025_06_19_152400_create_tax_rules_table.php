<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم القاعدة
            $table->string('code')->unique(); // رمز القاعدة
            $table->text('description')->nullable();
            $table->enum('rule_type', ['conditional', 'threshold', 'compound', 'reverse_charge', 'withholding']);
            $table->json('conditions'); // شروط تطبيق القاعدة
            $table->json('actions'); // الإجراءات المطبقة
            $table->integer('priority')->default(1); // أولوية التطبيق
            $table->boolean('is_active')->default(true);
            $table->date('effective_from')->nullable();
            $table->date('effective_to')->nullable();
            $table->json('applicable_entities')->nullable(); // الكيانات المطبقة (عملاء، موردين، أصناف)
            $table->text('legal_reference')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_rules');
    }
};

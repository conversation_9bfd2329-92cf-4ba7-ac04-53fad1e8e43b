{"name": "illuminate/broadcasting", "description": "The Illuminate Broadcasting package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "psr/log": "^1.0|^2.0|^3.0", "illuminate/bus": "^9.0", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/queue": "^9.0", "illuminate/support": "^9.0"}, "autoload": {"psr-4": {"Illuminate\\Broadcasting\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"ext-hash": "Required to use the <PERSON><PERSON> and <PERSON><PERSON><PERSON> broadcast drivers.", "ably/ably-php": "Required to use the Ably broadcast driver (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^6.0|^7.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
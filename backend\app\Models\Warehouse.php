<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Warehouse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'name_en',
        'code',
        'location',
        'manager_name',
        'phone',
        'is_active',
        'branch_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the branch that owns this warehouse.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the inventory items in this warehouse.
     */
    public function inventoryItems()
    {
        return $this->hasMany(InventoryItem::class);
    }

    /**
     * Get the stock movements for this warehouse.
     */
    public function stockMovements()
    {
        return $this->hasMany(StockMovement::class);
    }

    /**
     * Scope a query to only include active warehouses.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get current stock for a specific item.
     */
    public function getCurrentStock($itemId)
    {
        $inventoryItem = $this->inventoryItems()
                             ->where('item_id', $itemId)
                             ->first();

        return $inventoryItem ? $inventoryItem->current_stock : 0;
    }

    /**
     * Update stock for an item.
     */
    public function updateStock($itemId, $quantity, $type = 'in', $reference = null, $notes = null)
    {
        // Find or create inventory item
        $inventoryItem = $this->inventoryItems()
                             ->firstOrCreate(
                                 ['item_id' => $itemId],
                                 ['current_stock' => 0, 'reserved_stock' => 0]
                             );

        // Update stock based on type
        if ($type === 'in') {
            $inventoryItem->increment('current_stock', $quantity);
        } else {
            $inventoryItem->decrement('current_stock', $quantity);
        }

        // Create stock movement record
        $this->stockMovements()->create([
            'item_id' => $itemId,
            'movement_type' => $type,
            'quantity' => $quantity,
            'reference_type' => $reference['type'] ?? null,
            'reference_id' => $reference['id'] ?? null,
            'notes' => $notes,
            'user_id' => auth()->id(),
        ]);

        return $inventoryItem;
    }

    /**
     * Reserve stock for an item.
     */
    public function reserveStock($itemId, $quantity)
    {
        $inventoryItem = $this->inventoryItems()
                             ->where('item_id', $itemId)
                             ->first();

        if (!$inventoryItem || $inventoryItem->available_stock < $quantity) {
            return false;
        }

        $inventoryItem->increment('reserved_stock', $quantity);
        return true;
    }

    /**
     * Release reserved stock.
     */
    public function releaseReservedStock($itemId, $quantity)
    {
        $inventoryItem = $this->inventoryItems()
                             ->where('item_id', $itemId)
                             ->first();

        if ($inventoryItem) {
            $inventoryItem->decrement('reserved_stock', $quantity);
        }
    }

    /**
     * Get low stock items.
     */
    public function getLowStockItems()
    {
        return $this->inventoryItems()
                   ->with('item')
                   ->whereHas('item', function ($query) {
                       $query->whereColumn('current_stock', '<=', 'min_stock_level');
                   })
                   ->get();
    }

    /**
     * Get warehouse statistics.
     */
    public function getStatistics()
    {
        return [
            'total_items' => $this->inventoryItems()->count(),
            'total_stock_value' => $this->inventoryItems()
                                       ->with('item')
                                       ->get()
                                       ->sum(function ($inventoryItem) {
                                           return $inventoryItem->current_stock * $inventoryItem->item->cost_price;
                                       }),
            'low_stock_items' => $this->getLowStockItems()->count(),
            'out_of_stock_items' => $this->inventoryItems()
                                        ->where('current_stock', '<=', 0)
                                        ->count(),
        ];
    }

    /**
     * Generate unique warehouse code.
     */
    public static function generateWarehouseCode()
    {
        $lastWarehouse = static::orderBy('code', 'desc')->first();
        
        if ($lastWarehouse) {
            $lastNumber = (int) substr($lastWarehouse->code, 2); // Remove "WH" prefix
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return 'WH' . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}

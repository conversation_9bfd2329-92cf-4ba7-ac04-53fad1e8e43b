using AccountingSystem.Services;
using Microsoft.Extensions.DependencyInjection;

namespace AccountingSystem.Forms
{
    public partial class LoginForm : Form
    {
        private readonly IAuthService _authService;
        private readonly IServiceProvider _serviceProvider;

        public LoginForm(IAuthService authService, IServiceProvider serviceProvider)
        {
            _authService = authService;
            _serviceProvider = serviceProvider;
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            // Form properties
            this.Text = "تسجيل الدخول - نظام المحاسبة المتكامل";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;

            // Create controls
            CreateControls();
            SetupLayout();
            SetupEvents();
        }

        private void CreateControls()
        {
            // Logo/Title
            var titleLabel = new Label
            {
                Text = "نظام المحاسبة المتكامل",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 50
            };

            // Email
            var emailLabel = new Label
            {
                Text = "البريد الإلكتروني:",
                Font = new Font("Segoe UI", 10),
                Size = new Size(120, 25),
                Location = new Point(250, 80)
            };

            var emailTextBox = new TextBox
            {
                Name = "emailTextBox",
                Font = new Font("Segoe UI", 10),
                Size = new Size(200, 25),
                Location = new Point(50, 80),
                Text = "<EMAIL>" // Default for testing
            };

            // Password
            var passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10),
                Size = new Size(120, 25),
                Location = new Point(250, 120)
            };

            var passwordTextBox = new TextBox
            {
                Name = "passwordTextBox",
                Font = new Font("Segoe UI", 10),
                Size = new Size(200, 25),
                Location = new Point(50, 120),
                UseSystemPasswordChar = true,
                Text = "password" // Default for testing
            };

            // Remember Me
            var rememberCheckBox = new CheckBox
            {
                Name = "rememberCheckBox",
                Text = "تذكرني",
                Font = new Font("Segoe UI", 9),
                Size = new Size(100, 25),
                Location = new Point(50, 160)
            };

            // Login Button
            var loginButton = new Button
            {
                Name = "loginButton",
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(150, 200),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Cancel Button
            var cancelButton = new Button
            {
                Name = "cancelButton",
                Text = "إلغاء",
                Font = new Font("Segoe UI", 10),
                Size = new Size(80, 35),
                Location = new Point(260, 200),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                titleLabel,
                emailLabel,
                emailTextBox,
                passwordLabel,
                passwordTextBox,
                rememberCheckBox,
                loginButton,
                cancelButton
            });
        }

        private void SetupLayout()
        {
            // Set tab order
            var emailTextBox = this.Controls["emailTextBox"] as TextBox;
            var passwordTextBox = this.Controls["passwordTextBox"] as TextBox;
            var rememberCheckBox = this.Controls["rememberCheckBox"] as CheckBox;
            var loginButton = this.Controls["loginButton"] as Button;
            var cancelButton = this.Controls["cancelButton"] as Button;

            if (emailTextBox != null) emailTextBox.TabIndex = 0;
            if (passwordTextBox != null) passwordTextBox.TabIndex = 1;
            if (rememberCheckBox != null) rememberCheckBox.TabIndex = 2;
            if (loginButton != null) loginButton.TabIndex = 3;
            if (cancelButton != null) cancelButton.TabIndex = 4;

            // Set default button
            this.AcceptButton = loginButton;
            this.CancelButton = cancelButton;
        }

        private void SetupEvents()
        {
            var loginButton = this.Controls["loginButton"] as Button;
            var cancelButton = this.Controls["cancelButton"] as Button;
            var passwordTextBox = this.Controls["passwordTextBox"] as TextBox;

            if (loginButton != null)
                loginButton.Click += LoginButton_Click;

            if (cancelButton != null)
                cancelButton.Click += CancelButton_Click;

            if (passwordTextBox != null)
                passwordTextBox.KeyPress += PasswordTextBox_KeyPress;
        }

        private async void LoginButton_Click(object? sender, EventArgs e)
        {
            await PerformLogin();
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private async void PasswordTextBox_KeyPress(object? sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                e.Handled = true;
                await PerformLogin();
            }
        }

        private async Task PerformLogin()
        {
            var emailTextBox = this.Controls["emailTextBox"] as TextBox;
            var passwordTextBox = this.Controls["passwordTextBox"] as TextBox;
            var rememberCheckBox = this.Controls["rememberCheckBox"] as CheckBox;
            var loginButton = this.Controls["loginButton"] as Button;

            if (emailTextBox == null || passwordTextBox == null || rememberCheckBox == null || loginButton == null)
                return;

            var email = emailTextBox.Text.Trim();
            var password = passwordTextBox.Text;
            var rememberMe = rememberCheckBox.Checked;

            // Validation
            if (string.IsNullOrEmpty(email))
            {
                MessageBox.Show("يرجى إدخال البريد الإلكتروني", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                emailTextBox.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                passwordTextBox.Focus();
                return;
            }

            // Disable button and show loading
            loginButton.Enabled = false;
            loginButton.Text = "جاري تسجيل الدخول...";
            this.Cursor = Cursors.WaitCursor;

            try
            {
                var response = await _authService.LoginAsync(email, password, rememberMe);

                if (response.Success)
                {
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show(response.Message, "خطأ في تسجيل الدخول", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ غير متوقع: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Re-enable button
                loginButton.Enabled = true;
                loginButton.Text = "تسجيل الدخول";
                this.Cursor = Cursors.Default;
            }
        }
    }
}

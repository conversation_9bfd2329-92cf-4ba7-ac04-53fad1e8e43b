{"name": "illuminate/mail", "description": "The Illuminate Mail package.", "license": "MIT", "homepage": "https://laravel.com", "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.0.2", "illuminate/collections": "^9.0", "illuminate/container": "^9.0", "illuminate/contracts": "^9.0", "illuminate/macroable": "^9.0", "illuminate/support": "^9.0", "league/commonmark": "^2.2", "psr/log": "^1.0|^2.0|^3.0", "symfony/mailer": "^6.0", "tijsverkoyen/css-to-inline-styles": "^2.2.5"}, "autoload": {"psr-4": {"Illuminate\\Mail\\": ""}}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "suggest": {"aws/aws-sdk-php": "Required to use the SES mail driver (^3.235.5).", "symfony/http-client": "Required to use the Symfony API mail transports (^6.0).", "symfony/mailgun-mailer": "Required to enable support for the Mailgun mail transport (^6.0).", "symfony/postmark-mailer": "Required to enable support for the Postmark mail transport (^6.0)."}, "config": {"sort-packages": true}, "minimum-stability": "dev"}
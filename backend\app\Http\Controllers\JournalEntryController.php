<?php

namespace App\Http\Controllers;

use App\Models\JournalEntry;
use App\Models\JournalEntryDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class JournalEntryController extends Controller
{
    /**
     * عرض قائمة القيود المحاسبية
     */
    public function index(Request $request)
    {
        $query = JournalEntry::with(['user', 'approvedBy', 'branch', 'details.account', 'details.costCenter']);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('entry_number', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('reference', 'like', "%{$search}%");
            });
        }

        // فلترة حسب الحالة
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // فلترة حسب التاريخ
        if ($request->has('from_date')) {
            $query->where('entry_date', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->where('entry_date', '<=', $request->to_date);
        }

        // فلترة حسب الفرع
        if ($request->has('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // فلترة حسب المستخدم
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        $entries = $query->orderBy('entry_date', 'desc')
                        ->orderBy('entry_number', 'desc')
                        ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $entries,
        ]);
    }

    /**
     * عرض تفاصيل قيد محاسبي
     */
    public function show($id)
    {
        $entry = JournalEntry::with([
            'user', 
            'approvedBy', 
            'branch', 
            'details.account', 
            'details.costCenter'
        ])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => [
                'entry' => $entry,
                'is_balanced' => $entry->isBalanced(),
                'can_be_edited' => $entry->status === 'draft',
                'can_be_approved' => $entry->status === 'draft' && $entry->isBalanced(),
                'can_be_reversed' => $entry->status === 'approved',
            ],
        ]);
    }

    /**
     * إنشاء قيد محاسبي جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'entry_date' => 'required|date',
            'description' => 'required|string|max:500',
            'reference' => 'nullable|string|max:100',
            'branch_id' => 'required|exists:branches,id',
            'details' => 'required|array|min:2',
            'details.*.account_id' => 'required|exists:accounts,id',
            'details.*.cost_center_id' => 'nullable|exists:cost_centers,id',
            'details.*.debit' => 'required|numeric|min:0',
            'details.*.credit' => 'required|numeric|min:0',
            'details.*.description' => 'nullable|string|max:255',
        ]);

        // التحقق من توازن القيد
        $totalDebit = collect($request->details)->sum('debit');
        $totalCredit = collect($request->details)->sum('credit');

        if ($totalDebit != $totalCredit) {
            return response()->json([
                'success' => false,
                'message' => 'القيد غير متوازن. مجموع المدين يجب أن يساوي مجموع الدائن',
                'errors' => [
                    'balance' => [
                        'total_debit' => $totalDebit,
                        'total_credit' => $totalCredit,
                        'difference' => $totalDebit - $totalCredit,
                    ]
                ],
            ], 422);
        }

        // التحقق من وجود مبالغ في كلا الجانبين لنفس البند
        foreach ($request->details as $detail) {
            if ($detail['debit'] > 0 && $detail['credit'] > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن أن يحتوي البند على مبلغ في المدين والدائن معاً',
                ], 422);
            }
            if ($detail['debit'] == 0 && $detail['credit'] == 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب أن يحتوي كل بند على مبلغ في المدين أو الدائن',
                ], 422);
            }
        }

        DB::beginTransaction();
        try {
            // إنشاء القيد
            $entry = JournalEntry::create([
                'entry_number' => JournalEntry::generateEntryNumber(),
                'entry_date' => $request->entry_date,
                'description' => $request->description,
                'reference' => $request->reference,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
                'status' => 'draft',
                'user_id' => auth()->id(),
                'branch_id' => $request->branch_id,
                'attachments' => $request->attachments ?? [],
            ]);

            // إنشاء تفاصيل القيد
            foreach ($request->details as $detail) {
                $entry->details()->create([
                    'account_id' => $detail['account_id'],
                    'cost_center_id' => $detail['cost_center_id'] ?? null,
                    'debit' => $detail['debit'],
                    'credit' => $detail['credit'],
                    'description' => $detail['description'] ?? null,
                ]);
            }

            DB::commit();

            $entry->load(['details.account', 'details.costCenter']);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء القيد المحاسبي بنجاح',
                'data' => $entry,
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء القيد المحاسبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تحديث قيد محاسبي
     */
    public function update(Request $request, $id)
    {
        $entry = JournalEntry::findOrFail($id);

        if ($entry->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن تعديل قيد معتمد أو معكوس',
            ], 422);
        }

        $request->validate([
            'entry_date' => 'required|date',
            'description' => 'required|string|max:500',
            'reference' => 'nullable|string|max:100',
            'details' => 'required|array|min:2',
            'details.*.account_id' => 'required|exists:accounts,id',
            'details.*.cost_center_id' => 'nullable|exists:cost_centers,id',
            'details.*.debit' => 'required|numeric|min:0',
            'details.*.credit' => 'required|numeric|min:0',
            'details.*.description' => 'nullable|string|max:255',
        ]);

        // التحقق من توازن القيد
        $totalDebit = collect($request->details)->sum('debit');
        $totalCredit = collect($request->details)->sum('credit');

        if ($totalDebit != $totalCredit) {
            return response()->json([
                'success' => false,
                'message' => 'القيد غير متوازن. مجموع المدين يجب أن يساوي مجموع الدائن',
            ], 422);
        }

        DB::beginTransaction();
        try {
            // تحديث القيد
            $entry->update([
                'entry_date' => $request->entry_date,
                'description' => $request->description,
                'reference' => $request->reference,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
                'attachments' => $request->attachments ?? [],
            ]);

            // حذف التفاصيل القديمة
            $entry->details()->delete();

            // إنشاء التفاصيل الجديدة
            foreach ($request->details as $detail) {
                $entry->details()->create([
                    'account_id' => $detail['account_id'],
                    'cost_center_id' => $detail['cost_center_id'] ?? null,
                    'debit' => $detail['debit'],
                    'credit' => $detail['credit'],
                    'description' => $detail['description'] ?? null,
                ]);
            }

            DB::commit();

            $entry->load(['details.account', 'details.costCenter']);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث القيد المحاسبي بنجاح',
                'data' => $entry,
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث القيد المحاسبي',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * حذف قيد محاسبي
     */
    public function destroy($id)
    {
        $entry = JournalEntry::findOrFail($id);

        if ($entry->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف قيد معتمد أو معكوس',
            ], 422);
        }

        $entry->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف القيد المحاسبي بنجاح',
        ]);
    }

    /**
     * اعتماد قيد محاسبي
     */
    public function approve($id)
    {
        $entry = JournalEntry::findOrFail($id);

        if ($entry->status !== 'draft') {
            return response()->json([
                'success' => false,
                'message' => 'القيد معتمد مسبقاً أو معكوس',
            ], 422);
        }

        if (!$entry->isBalanced()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن اعتماد قيد غير متوازن',
            ], 422);
        }

        try {
            $entry->approve(auth()->id());

            return response()->json([
                'success' => true,
                'message' => 'تم اعتماد القيد المحاسبي بنجاح',
                'data' => $entry,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * عكس قيد محاسبي
     */
    public function reverse(Request $request, $id)
    {
        $entry = JournalEntry::findOrFail($id);

        $request->validate([
            'description' => 'nullable|string|max:500',
        ]);

        try {
            $reverseEntry = $entry->reverse($request->description);

            return response()->json([
                'success' => true,
                'message' => 'تم عكس القيد المحاسبي بنجاح',
                'data' => [
                    'original_entry' => $entry,
                    'reverse_entry' => $reverseEntry,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 422);
        }
    }

    /**
     * الحصول على إحصائيات القيود
     */
    public function getStatistics(Request $request)
    {
        $fromDate = $request->from_date ?? now()->startOfMonth()->toDateString();
        $toDate = $request->to_date ?? now()->toDateString();

        $stats = [
            'total_entries' => JournalEntry::whereBetween('entry_date', [$fromDate, $toDate])->count(),
            'draft_entries' => JournalEntry::whereBetween('entry_date', [$fromDate, $toDate])->draft()->count(),
            'approved_entries' => JournalEntry::whereBetween('entry_date', [$fromDate, $toDate])->approved()->count(),
            'reversed_entries' => JournalEntry::whereBetween('entry_date', [$fromDate, $toDate])->where('status', 'reversed')->count(),
            'total_amount' => JournalEntry::whereBetween('entry_date', [$fromDate, $toDate])->approved()->sum('total_debit'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }
}

HTML.SafeEmbed
TYPE: bool
VERSION: 3.1.1
DEFAULT: false
--DESCRIPTION--
<p>
    Whether or not to permit embed tags in documents, with a number of extra
    security features added to prevent script execution. This is similar to
    what websites like MySpace do to embed tags. Embed is a proprietary
    element and will cause your website to stop validating; you should
    see if you can use %Output.FlashCompat with %HTML.SafeObject instead
    first.</p>
--# vim: et sw=4 sts=4

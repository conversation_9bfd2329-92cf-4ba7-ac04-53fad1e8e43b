<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح لك بالوصول',
                'error' => 'Unauthenticated',
            ], 401);
        }

        if (!$request->user()->hasPermission($permission)) {
            return response()->json([
                'success' => false,
                'message' => 'ليس لديك صلاحية للقيام بهذا الإجراء',
                'error' => 'Insufficient permissions',
                'required_permission' => $permission,
            ], 403);
        }

        return $next($request);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_group_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tax_group_id')->constrained()->onDelete('cascade');
            $table->foreignId('tax_type_id')->constrained()->onDelete('cascade');
            $table->decimal('priority', 3, 0)->default(1); // أولوية التطبيق
            $table->boolean('is_compound')->default(false); // ضريبة مركبة
            $table->decimal('min_amount', 15, 2)->nullable(); // الحد الأدنى للتطبيق
            $table->decimal('max_amount', 15, 2)->nullable(); // الحد الأقصى للتطبيق
            $table->json('conditions')->nullable(); // شروط إضافية
            $table->timestamps();

            $table->unique(['tax_group_id', 'tax_type_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_group_items');
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaxExemption extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'exemption_type',
        'exemption_percentage',
        'exemption_amount',
        'description',
        'conditions',
        'applicable_tax_types',
        'valid_from',
        'valid_to',
        'is_active',
        'legal_reference',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'exemption_percentage' => 'decimal:2',
        'exemption_amount' => 'decimal:2',
        'conditions' => 'array',
        'applicable_tax_types' => 'array',
        'valid_from' => 'date',
        'valid_to' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Calculate exemption amount for a given tax amount.
     */
    public function calculateExemption($taxAmount, $baseAmount = null, $conditions = [])
    {
        if (!$this->isApplicable($conditions)) {
            return 0;
        }

        switch ($this->exemption_type) {
            case 'full':
                return $taxAmount;
                
            case 'partial':
                if ($this->exemption_percentage) {
                    return $taxAmount * ($this->exemption_percentage / 100);
                } elseif ($this->exemption_amount) {
                    return min($this->exemption_amount, $taxAmount);
                }
                break;
                
            case 'conditional':
                return $this->calculateConditionalExemption($taxAmount, $baseAmount, $conditions);
        }

        return 0;
    }

    /**
     * Calculate conditional exemption.
     */
    protected function calculateConditionalExemption($taxAmount, $baseAmount, $conditions)
    {
        if (!$this->conditions) {
            return 0;
        }

        // Example conditional logic - can be extended
        foreach ($this->conditions as $condition) {
            if (!$this->checkCondition($condition, $baseAmount, $conditions)) {
                return 0;
            }
        }

        // If all conditions are met, apply exemption
        if ($this->exemption_percentage) {
            return $taxAmount * ($this->exemption_percentage / 100);
        } elseif ($this->exemption_amount) {
            return min($this->exemption_amount, $taxAmount);
        }

        return $taxAmount; // Full exemption if no specific amount/percentage
    }

    /**
     * Check if a specific condition is met.
     */
    protected function checkCondition($condition, $baseAmount, $providedConditions)
    {
        $type = $condition['type'] ?? '';
        $value = $condition['value'] ?? null;
        $operator = $condition['operator'] ?? '=';

        switch ($type) {
            case 'amount_threshold':
                return $this->compareValues($baseAmount, $value, $operator);
                
            case 'customer_type':
                $customerType = $providedConditions['customer_type'] ?? null;
                return $customerType === $value;
                
            case 'item_category':
                $itemCategory = $providedConditions['item_category'] ?? null;
                return $itemCategory === $value;
                
            case 'date_range':
                $currentDate = now()->toDateString();
                $from = $condition['from'] ?? null;
                $to = $condition['to'] ?? null;
                
                if ($from && $currentDate < $from) return false;
                if ($to && $currentDate > $to) return false;
                
                return true;
                
            default:
                return true;
        }
    }

    /**
     * Compare values based on operator.
     */
    protected function compareValues($actual, $expected, $operator)
    {
        switch ($operator) {
            case '>': return $actual > $expected;
            case '>=': return $actual >= $expected;
            case '<': return $actual < $expected;
            case '<=': return $actual <= $expected;
            case '=': return $actual == $expected;
            case '!=': return $actual != $expected;
            default: return true;
        }
    }

    /**
     * Check if exemption is applicable.
     */
    public function isApplicable($conditions = [])
    {
        if (!$this->is_active) {
            return false;
        }

        $currentDate = now()->toDateString();
        
        if ($this->valid_from && $currentDate < $this->valid_from) {
            return false;
        }
        
        if ($this->valid_to && $currentDate > $this->valid_to) {
            return false;
        }

        return true;
    }

    /**
     * Check if exemption applies to a specific tax type.
     */
    public function appliesToTaxType($taxTypeId)
    {
        if (!$this->applicable_tax_types) {
            return true; // Applies to all if not specified
        }

        return in_array($taxTypeId, $this->applicable_tax_types);
    }

    /**
     * Scope a query to only include active exemptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include valid exemptions for a date.
     */
    public function scopeValidOn($query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        
        return $query->where('is_active', true)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('valid_from')
                          ->orWhere('valid_from', '<=', $date);
                    })
                    ->where(function ($q) use ($date) {
                        $q->whereNull('valid_to')
                          ->orWhere('valid_to', '>=', $date);
                    });
    }

    /**
     * Scope a query to filter by exemption type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('exemption_type', $type);
    }
}

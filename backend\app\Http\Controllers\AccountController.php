<?php

namespace App\Http\Controllers;

use App\Models\Account;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class AccountController extends Controller
{
    /**
     * عرض شجرة الحسابات
     */
    public function index(Request $request)
    {
        $query = Account::with(['parent', 'children']);

        // البحث
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('name_en', 'like', "%{$search}%");
            });
        }

        // فلترة حسب النوع
        if ($request->has('account_type')) {
            $query->where('account_type', $request->account_type);
        }

        // فلترة حسب الحالة
        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        // فلترة حسب المستوى
        if ($request->has('level')) {
            $query->where('level', $request->level);
        }

        // إذا كان المطلوب الحسابات الرئيسية فقط
        if ($request->boolean('parents_only')) {
            $query->whereNull('parent_id');
        }

        // إذا كان المطلوب الحسابات الفرعية فقط (التي لا تحتوي على حسابات فرعية)
        if ($request->boolean('leaves_only')) {
            $query->whereDoesntHave('children');
        }

        $accounts = $query->orderBy('code')->get();

        // إذا كان المطلوب عرض الشجرة
        if ($request->boolean('tree_format')) {
            $accounts = $this->buildAccountTree($accounts);
        }

        return response()->json([
            'success' => true,
            'data' => $accounts,
        ]);
    }

    /**
     * عرض تفاصيل حساب محدد
     */
    public function show($id)
    {
        $account = Account::with(['parent', 'children', 'journalEntryDetails'])
                         ->findOrFail($id);

        // حساب الرصيد الحالي
        $currentBalance = $account->getBalanceAsOf(now()->toDateString());

        return response()->json([
            'success' => true,
            'data' => [
                'account' => $account,
                'current_balance' => $currentBalance,
                'can_be_deleted' => $account->canBeDeleted(),
            ],
        ]);
    }

    /**
     * إنشاء حساب جديد
     */
    public function store(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:20|unique:accounts',
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'parent_id' => 'nullable|exists:accounts,id',
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'opening_balance' => 'nullable|numeric',
            'description' => 'nullable|string',
        ]);

        // تحديد المستوى
        $level = 1;
        if ($request->parent_id) {
            $parent = Account::find($request->parent_id);
            $level = $parent->level + 1;
        }

        $account = Account::create([
            'code' => $request->code,
            'name' => $request->name,
            'name_en' => $request->name_en,
            'parent_id' => $request->parent_id,
            'account_type' => $request->account_type,
            'level' => $level,
            'opening_balance' => $request->opening_balance ?? 0,
            'current_balance' => $request->opening_balance ?? 0,
            'description' => $request->description,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الحساب بنجاح',
            'data' => $account,
        ], 201);
    }

    /**
     * تحديث حساب
     */
    public function update(Request $request, $id)
    {
        $account = Account::findOrFail($id);

        $request->validate([
            'code' => ['required', 'string', 'max:20', Rule::unique('accounts')->ignore($id)],
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'parent_id' => [
                'nullable',
                'exists:accounts,id',
                function ($attribute, $value, $fail) use ($account) {
                    // التأكد من عدم جعل الحساب والد لنفسه أو لأحد أحفاده
                    if ($value == $account->id) {
                        $fail('لا يمكن جعل الحساب والد لنفسه');
                    }
                    
                    if ($value && $account->descendants()->pluck('id')->contains($value)) {
                        $fail('لا يمكن جعل الحساب والد لأحد أحفاده');
                    }
                },
            ],
            'account_type' => 'required|in:asset,liability,equity,revenue,expense',
            'description' => 'nullable|string',
        ]);

        // تحديد المستوى الجديد
        $level = 1;
        if ($request->parent_id) {
            $parent = Account::find($request->parent_id);
            $level = $parent->level + 1;
        }

        $account->update([
            'code' => $request->code,
            'name' => $request->name,
            'name_en' => $request->name_en,
            'parent_id' => $request->parent_id,
            'account_type' => $request->account_type,
            'level' => $level,
            'description' => $request->description,
        ]);

        // تحديث مستويات الحسابات الفرعية
        $this->updateChildrenLevels($account);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الحساب بنجاح',
            'data' => $account,
        ]);
    }

    /**
     * حذف حساب
     */
    public function destroy($id)
    {
        $account = Account::findOrFail($id);

        if (!$account->canBeDeleted()) {
            return response()->json([
                'success' => false,
                'message' => 'لا يمكن حذف هذا الحساب لأنه يحتوي على حسابات فرعية أو قيود محاسبية',
            ], 422);
        }

        $account->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الحساب بنجاح',
        ]);
    }

    /**
     * تفعيل/إلغاء تفعيل حساب
     */
    public function toggleStatus($id)
    {
        $account = Account::findOrFail($id);
        $account->update(['is_active' => !$account->is_active]);

        $status = $account->is_active ? 'تم تفعيل' : 'تم إلغاء تفعيل';

        return response()->json([
            'success' => true,
            'message' => "{$status} الحساب بنجاح",
            'data' => $account,
        ]);
    }

    /**
     * الحصول على كشف حساب
     */
    public function statement(Request $request, $id)
    {
        $request->validate([
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
        ]);

        $account = Account::findOrFail($id);
        $fromDate = $request->from_date ?? now()->startOfMonth()->toDateString();
        $toDate = $request->to_date ?? now()->toDateString();

        // الرصيد الافتتاحي
        $openingBalance = $account->getBalanceAsOf($fromDate);

        // الحركات خلال الفترة
        $movements = $account->journalEntryDetails()
                           ->with(['journalEntry', 'costCenter'])
                           ->whereHas('journalEntry', function ($query) use ($fromDate, $toDate) {
                               $query->whereBetween('entry_date', [$fromDate, $toDate])
                                     ->where('status', 'approved');
                           })
                           ->orderBy('created_at')
                           ->get();

        // حساب الرصيد الجاري
        $runningBalance = $openingBalance;
        $totalDebit = 0;
        $totalCredit = 0;

        foreach ($movements as $movement) {
            $totalDebit += $movement->debit;
            $totalCredit += $movement->credit;
            
            if ($account->account_type === 'asset' || $account->account_type === 'expense') {
                $runningBalance += $movement->debit - $movement->credit;
            } else {
                $runningBalance += $movement->credit - $movement->debit;
            }
            
            $movement->running_balance = $runningBalance;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'account' => $account,
                'period' => [
                    'from' => $fromDate,
                    'to' => $toDate,
                ],
                'opening_balance' => $openingBalance,
                'closing_balance' => $runningBalance,
                'total_debit' => $totalDebit,
                'total_credit' => $totalCredit,
                'movements' => $movements,
            ],
        ]);
    }

    /**
     * الحصول على الحسابات النشطة فقط
     */
    public function getActiveAccounts()
    {
        $accounts = Account::active()
                          ->orderBy('code')
                          ->get(['id', 'code', 'name', 'account_type', 'level']);

        return response()->json([
            'success' => true,
            'data' => $accounts,
        ]);
    }

    /**
     * بناء شجرة الحسابات
     */
    protected function buildAccountTree($accounts, $parentId = null)
    {
        $tree = [];
        
        foreach ($accounts as $account) {
            if ($account->parent_id == $parentId) {
                $children = $this->buildAccountTree($accounts, $account->id);
                if ($children) {
                    $account->children = $children;
                }
                $tree[] = $account;
            }
        }
        
        return $tree;
    }

    /**
     * تحديث مستويات الحسابات الفرعية
     */
    protected function updateChildrenLevels($account)
    {
        foreach ($account->children as $child) {
            $child->update(['level' => $account->level + 1]);
            $this->updateChildrenLevels($child);
        }
    }
}

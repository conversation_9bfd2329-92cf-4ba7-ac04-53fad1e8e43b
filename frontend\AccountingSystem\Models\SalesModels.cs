namespace AccountingSystem.Models
{
    public class Customer : BaseModel
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _nameEn = string.Empty;
        private string _phone = string.Empty;
        private string _email = string.Empty;
        private string _address = string.Empty;
        private string _taxNumber = string.Empty;
        private decimal _creditLimit;
        private int _paymentTerms;
        private string _customerType = string.Empty;
        private decimal _openingBalance;
        private decimal _currentBalance;
        private bool _isActive = true;
        private string _notes = string.Empty;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string NameEn
        {
            get => _nameEn;
            set => SetProperty(ref _nameEn, value);
        }

        public string Phone
        {
            get => _phone;
            set => SetProperty(ref _phone, value);
        }

        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        public string TaxNumber
        {
            get => _taxNumber;
            set => SetProperty(ref _taxNumber, value);
        }

        public decimal CreditLimit
        {
            get => _creditLimit;
            set => SetProperty(ref _creditLimit, value);
        }

        public int PaymentTerms
        {
            get => _paymentTerms;
            set => SetProperty(ref _paymentTerms, value);
        }

        public string CustomerType
        {
            get => _customerType;
            set => SetProperty(ref _customerType, value);
        }

        public decimal OpeningBalance
        {
            get => _openingBalance;
            set => SetProperty(ref _openingBalance, value);
        }

        public decimal CurrentBalance
        {
            get => _currentBalance;
            set => SetProperty(ref _currentBalance, value);
        }

        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public string CustomerTypeDisplay => CustomerType switch
        {
            "individual" => "فرد",
            "company" => "شركة",
            _ => CustomerType
        };

        public decimal AvailableCredit => Math.Max(0, CreditLimit - CurrentBalance);
        public bool HasExceededCreditLimit => CurrentBalance > CreditLimit;
    }

    public class Supplier : BaseModel
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _nameEn = string.Empty;
        private string _phone = string.Empty;
        private string _email = string.Empty;
        private string _address = string.Empty;
        private string _taxNumber = string.Empty;
        private int _paymentTerms;
        private decimal _openingBalance;
        private decimal _currentBalance;
        private bool _isActive = true;
        private string _notes = string.Empty;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string NameEn
        {
            get => _nameEn;
            set => SetProperty(ref _nameEn, value);
        }

        public string Phone
        {
            get => _phone;
            set => SetProperty(ref _phone, value);
        }

        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        public string TaxNumber
        {
            get => _taxNumber;
            set => SetProperty(ref _taxNumber, value);
        }

        public int PaymentTerms
        {
            get => _paymentTerms;
            set => SetProperty(ref _paymentTerms, value);
        }

        public decimal OpeningBalance
        {
            get => _openingBalance;
            set => SetProperty(ref _openingBalance, value);
        }

        public decimal CurrentBalance
        {
            get => _currentBalance;
            set => SetProperty(ref _currentBalance, value);
        }

        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }
    }

    public class Item : BaseModel
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _nameEn = string.Empty;
        private int _categoryId;
        private int _unitId;
        private decimal _costPrice;
        private decimal _sellingPrice;
        private decimal _minStockLevel;
        private decimal _maxStockLevel;
        private bool _isTaxable = true;
        private int? _taxTypeId;
        private bool _isActive = true;
        private string _description = string.Empty;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string NameEn
        {
            get => _nameEn;
            set => SetProperty(ref _nameEn, value);
        }

        public int CategoryId
        {
            get => _categoryId;
            set => SetProperty(ref _categoryId, value);
        }

        public int UnitId
        {
            get => _unitId;
            set => SetProperty(ref _unitId, value);
        }

        public decimal CostPrice
        {
            get => _costPrice;
            set => SetProperty(ref _costPrice, value);
        }

        public decimal SellingPrice
        {
            get => _sellingPrice;
            set => SetProperty(ref _sellingPrice, value);
        }

        public decimal MinStockLevel
        {
            get => _minStockLevel;
            set => SetProperty(ref _minStockLevel, value);
        }

        public decimal MaxStockLevel
        {
            get => _maxStockLevel;
            set => SetProperty(ref _maxStockLevel, value);
        }

        public bool IsTaxable
        {
            get => _isTaxable;
            set => SetProperty(ref _isTaxable, value);
        }

        public int? TaxTypeId
        {
            get => _taxTypeId;
            set => SetProperty(ref _taxTypeId, value);
        }

        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public string DisplayName => $"{Code} - {Name}";
        public decimal ProfitMargin => CostPrice > 0 ? ((SellingPrice - CostPrice) / CostPrice) * 100 : 0;
    }
}

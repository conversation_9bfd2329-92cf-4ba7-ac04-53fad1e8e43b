<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaxType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'rate',
        'type',
        'calculation_method',
        'is_active',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the items that use this tax type.
     */
    public function items()
    {
        return $this->hasMany(Item::class);
    }

    /**
     * Calculate tax amount for a given base amount.
     */
    public function calculateTax($baseAmount, $customRate = null)
    {
        $rate = $customRate ?? $this->rate;
        
        if ($this->type === 'percentage') {
            if ($this->calculation_method === 'inclusive') {
                // الضريبة مشمولة في السعر
                return $baseAmount - ($baseAmount / (1 + ($rate / 100)));
            } else {
                // الضريبة مضافة على السعر
                return $baseAmount * ($rate / 100);
            }
        } else {
            // ضريبة ثابتة
            return $rate;
        }
    }

    /**
     * Calculate base amount from total amount (for inclusive taxes).
     */
    public function calculateBaseAmount($totalAmount, $customRate = null)
    {
        $rate = $customRate ?? $this->rate;
        
        if ($this->type === 'percentage' && $this->calculation_method === 'inclusive') {
            return $totalAmount / (1 + ($rate / 100));
        }
        
        return $totalAmount;
    }

    /**
     * Calculate total amount including tax.
     */
    public function calculateTotalAmount($baseAmount, $customRate = null)
    {
        $rate = $customRate ?? $this->rate;
        
        if ($this->type === 'percentage') {
            if ($this->calculation_method === 'inclusive') {
                return $baseAmount;
            } else {
                return $baseAmount * (1 + ($rate / 100));
            }
        } else {
            return $baseAmount + $rate;
        }
    }

    /**
     * Scope a query to only include active tax types.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include percentage-based taxes.
     */
    public function scopePercentage($query)
    {
        return $query->where('type', 'percentage');
    }

    /**
     * Scope a query to only include fixed-amount taxes.
     */
    public function scopeFixed($query)
    {
        return $query->where('type', 'fixed');
    }

    /**
     * Get formatted rate for display.
     */
    public function getFormattedRateAttribute()
    {
        if ($this->type === 'percentage') {
            return $this->rate . '%';
        } else {
            return number_format($this->rate, 2) . ' ريال';
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tax_types', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الضريبة
            $table->string('code')->unique(); // رمز الضريبة (VAT, SALES_TAX, etc.)
            $table->decimal('rate', 5, 2); // معدل الضريبة (15.00 للضريبة المضافة)
            $table->enum('type', ['percentage', 'fixed'])->default('percentage'); // نوع الضريبة
            $table->enum('calculation_method', ['inclusive', 'exclusive'])->default('exclusive'); // طريقة الحساب
            $table->boolean('is_active')->default(true);
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tax_types');
    }
};

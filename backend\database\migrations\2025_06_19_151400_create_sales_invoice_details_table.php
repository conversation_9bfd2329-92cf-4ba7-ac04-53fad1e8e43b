<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales_invoice_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('sales_invoice_id')->constrained()->onDelete('cascade');
            $table->foreignId('item_id')->constrained()->onDelete('restrict');
            $table->decimal('quantity', 10, 3);
            $table->decimal('unit_price', 10, 2); // السعر قبل الضريبة
            $table->decimal('discount_amount', 10, 2)->default(0); // خصم البند
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة خصم البند
            $table->decimal('net_amount', 10, 2)->default(0); // المبلغ بعد الخصم قبل الضريبة
            $table->decimal('tax_rate', 5, 2)->default(0); // معدل الضريبة المطبق
            $table->decimal('tax_amount', 10, 2)->default(0); // مبلغ الضريبة
            $table->decimal('total_amount', 10, 2)->default(0); // المبلغ الإجمالي شامل الضريبة
            $table->foreignId('tax_type_id')->nullable()->constrained()->onDelete('set null');
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales_invoice_details');
    }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaxGroup extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'is_active',
        'applicable_countries',
        'effective_from',
        'effective_to',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'applicable_countries' => 'array',
        'effective_from' => 'date',
        'effective_to' => 'date',
    ];

    /**
     * Get the tax types in this group.
     */
    public function taxTypes()
    {
        return $this->belongsToMany(TaxType::class, 'tax_group_items')
                    ->withPivot(['priority', 'is_compound', 'min_amount', 'max_amount', 'conditions'])
                    ->withTimestamps()
                    ->orderBy('pivot_priority');
    }

    /**
     * Get the items that use this tax group.
     */
    public function items()
    {
        return $this->hasMany(Item::class);
    }

    /**
     * Get active tax types in this group.
     */
    public function activeTaxTypes()
    {
        return $this->taxTypes()->where('tax_types.is_active', true);
    }

    /**
     * Get compound tax types in this group.
     */
    public function compoundTaxTypes()
    {
        return $this->taxTypes()->wherePivot('is_compound', true);
    }

    /**
     * Calculate total tax for an amount using this group.
     */
    public function calculateGroupTax($amount, $conditions = [])
    {
        $totalTax = 0;
        $taxBreakdown = [];
        $compoundBase = $amount;

        foreach ($this->activeTaxTypes as $taxType) {
            $pivot = $taxType->pivot;
            
            // Check amount limits
            if ($pivot->min_amount && $amount < $pivot->min_amount) {
                continue;
            }
            if ($pivot->max_amount && $amount > $pivot->max_amount) {
                continue;
            }

            // Check additional conditions
            if ($pivot->conditions && !$this->checkConditions($pivot->conditions, $conditions)) {
                continue;
            }

            $baseAmount = $pivot->is_compound ? $compoundBase : $amount;
            $taxAmount = $taxType->calculateTax($baseAmount);

            $totalTax += $taxAmount;
            $taxBreakdown[] = [
                'tax_type_id' => $taxType->id,
                'tax_type_name' => $taxType->name,
                'base_amount' => $baseAmount,
                'tax_rate' => $taxType->rate,
                'tax_amount' => $taxAmount,
                'is_compound' => $pivot->is_compound,
            ];

            // For compound taxes, add tax to base for next calculation
            if ($pivot->is_compound) {
                $compoundBase += $taxAmount;
            }
        }

        return [
            'total_tax' => $totalTax,
            'breakdown' => $taxBreakdown,
            'final_amount' => $amount + $totalTax,
        ];
    }

    /**
     * Check if conditions are met.
     */
    protected function checkConditions($conditions, $providedConditions)
    {
        if (!is_array($conditions)) {
            return true;
        }

        foreach ($conditions as $key => $value) {
            if (!isset($providedConditions[$key]) || $providedConditions[$key] !== $value) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if group is currently effective.
     */
    public function isEffective($date = null)
    {
        $date = $date ?? now()->toDateString();
        
        if ($this->effective_from && $date < $this->effective_from) {
            return false;
        }
        
        if ($this->effective_to && $date > $this->effective_to) {
            return false;
        }

        return $this->is_active;
    }

    /**
     * Scope a query to only include active groups.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include effective groups.
     */
    public function scopeEffective($query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        
        return $query->where('is_active', true)
                    ->where(function ($q) use ($date) {
                        $q->whereNull('effective_from')
                          ->orWhere('effective_from', '<=', $date);
                    })
                    ->where(function ($q) use ($date) {
                        $q->whereNull('effective_to')
                          ->orWhere('effective_to', '>=', $date);
                    });
    }
}
